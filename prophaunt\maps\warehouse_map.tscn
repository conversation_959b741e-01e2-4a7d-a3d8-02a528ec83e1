[gd_scene load_steps=4 format=3 uid="uid://bh8warehouse1"]

[sub_resource type="BoxMesh" id="BoxMesh_1"]
size = Vector3(50, 1, 50)

[sub_resource type="BoxShape3D" id="BoxShape3D_1"]
size = Vector3(50, 1, 50)

[sub_resource type="Environment" id="Environment_1"]
background_mode = 1
background_color = Color(0.3, 0.3, 0.4, 1)
ambient_light_source = 2
ambient_light_color = Color(0.8, 0.8, 0.9, 1)
ambient_light_energy = 0.3

[node name="WarehouseMap" type="Node3D"]

[node name="Floor" type="StaticBody3D" parent="."]

[node name="FloorMesh" type="MeshInstance3D" parent="Floor"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, -0.5, 0)
mesh = SubResource("BoxMesh_1")

[node name="FloorCollision" type="CollisionShape3D" parent="Floor"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, -0.5, 0)
shape = SubResource("BoxShape3D_1")

[node name="Walls" type="Node3D" parent="."]

[node name="WallNorth" type="StaticBody3D" parent="Walls"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 2.5, 25)

[node name="WallMesh" type="MeshInstance3D" parent="Walls/WallNorth"]
mesh = SubResource("BoxMesh_1")

[node name="WallCollision" type="CollisionShape3D" parent="Walls/WallNorth"]
shape = SubResource("BoxShape3D_1")

[node name="WallSouth" type="StaticBody3D" parent="Walls"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 2.5, -25)

[node name="WallMesh" type="MeshInstance3D" parent="Walls/WallSouth"]
mesh = SubResource("BoxMesh_1")

[node name="WallCollision" type="CollisionShape3D" parent="Walls/WallSouth"]
shape = SubResource("BoxShape3D_1")

[node name="WallEast" type="StaticBody3D" parent="Walls"]
transform = Transform3D(0, 0, 1, 0, 1, 0, -1, 0, 0, 25, 2.5, 0)

[node name="WallMesh" type="MeshInstance3D" parent="Walls/WallEast"]
mesh = SubResource("BoxMesh_1")

[node name="WallCollision" type="CollisionShape3D" parent="Walls/WallEast"]
shape = SubResource("BoxShape3D_1")

[node name="WallWest" type="StaticBody3D" parent="Walls"]
transform = Transform3D(0, 0, 1, 0, 1, 0, -1, 0, 0, -25, 2.5, 0)

[node name="WallMesh" type="MeshInstance3D" parent="Walls/WallWest"]
mesh = SubResource("BoxMesh_1")

[node name="WallCollision" type="CollisionShape3D" parent="Walls/WallWest"]
shape = SubResource("BoxShape3D_1")

[node name="Props" type="Node3D" parent="."]

[node name="Box1" type="StaticBody3D" parent="Props"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 5, 1, 5)

[node name="BoxMesh" type="MeshInstance3D" parent="Props/Box1"]
mesh = SubResource("BoxMesh_1")

[node name="BoxCollision" type="CollisionShape3D" parent="Props/Box1"]
shape = SubResource("BoxShape3D_1")

[node name="Box2" type="StaticBody3D" parent="Props"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -8, 1, 12)

[node name="BoxMesh" type="MeshInstance3D" parent="Props/Box2"]
mesh = SubResource("BoxMesh_1")

[node name="BoxCollision" type="CollisionShape3D" parent="Props/Box2"]
shape = SubResource("BoxShape3D_1")

[node name="Box3" type="StaticBody3D" parent="Props"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 15, 1, -5)

[node name="BoxMesh" type="MeshInstance3D" parent="Props/Box3"]
mesh = SubResource("BoxMesh_1")

[node name="BoxCollision" type="CollisionShape3D" parent="Props/Box3"]
shape = SubResource("BoxShape3D_1")

[node name="Barrel1" type="StaticBody3D" parent="Props"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -10, 1, -8)

[node name="BarrelMesh" type="MeshInstance3D" parent="Props/Barrel1"]
mesh = SubResource("BoxMesh_1")

[node name="BarrelCollision" type="CollisionShape3D" parent="Props/Barrel1"]
shape = SubResource("BoxShape3D_1")

[node name="Lighting" type="Node3D" parent="."]

[node name="MainLight" type="DirectionalLight3D" parent="Lighting"]
transform = Transform3D(0.707107, -0.5, 0.5, 0, 0.707107, 0.707107, -0.707107, -0.5, 0.5, 0, 10, 0)
light_energy = 0.8

[node name="AmbientLight" type="DirectionalLight3D" parent="Lighting"]
transform = Transform3D(1, 0, 0, 0, 0.866025, 0.5, 0, -0.5, 0.866025, 0, 5, 0)
light_energy = 0.3
light_color = Color(0.8, 0.9, 1, 1)

[node name="SpawnAreas" type="Node3D" parent="."]

[node name="HaunterSpawn" type="Node3D" parent="SpawnAreas"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 1, -20)

[node name="PropSpawnCenter" type="Node3D" parent="SpawnAreas"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 1, 10)

[node name="Environment" type="Node3D" parent="."]

[node name="WorldEnvironment" type="WorldEnvironment" parent="Environment"]
environment = SubResource("Environment_1")
