extends ProphauntServerState

var lobby_timer = 0
var lobby_update_counter = 0

func set_state():
	print("Set State to Prophaunt Lobby")
	if server.peer:
		server.peer.refuse_new_connections = false
	lobby_update_counter = 0
	lobby_timer = 0
	server.state = Constants.ServerState.Lobby
	lobby_timer = Constants.LOBBY_TIME

func run(delta):
	if not server:
		return

	# Check if we have enough players to start (minimum 2 for testing, ideally 4+)
	var min_players = 2  # Can be adjusted
	if server.count_players_ready() >= min_players:
		start_prophaunt_round()
		return

	lobby_timer -= delta
	if lobby_timer <= 0:
		if server.is_all_players_ready():
			start_prophaunt_round()
			return
		elif lobby_timer <= -3:
			start_prophaunt_round()
			return
	
	if server.count_non_bot_players() == 0:
		lobby_timer = Constants.LOBBY_TIME
		TickManager.reset()

	lobby_update_counter += delta
	if lobby_update_counter < Constants.SERVER_LOBBY_SYNC:
		return
	
	lobby_update_counter = 0
	
	# Send Prophaunt-specific lobby data
	var lobby_data = {
		"j": server.players_count,
		"s": min_players,
		"t": int(lobby_timer) + 1,
		"mode": "prophaunt",
		"round": current_round,
		"rounds_on_map": rounds_played_on_map,
	}
	
	for key in server.players_data.keys():
		if server.is_bot(key):
			continue
		if server.is_dc(key):
			continue
		multiplayer.rpc(key, ClientRPC, "prophaunt_lobby_update", [lobby_data])

func start_prophaunt_round():
	"""Start a new Prophaunt round"""
	print("Starting Prophaunt Round ", current_round)
	
	# Load map if this is the first round on this map
	if rounds_played_on_map == 0:
		load_prophaunt_map()
	
	# Assign teams
	assign_teams()
	
	# Initialize round
	round_timer = Constants.PROPHAUNT_ROUND_TIME
	round_start_time = Time.get_ticks_msec()
	
	# Spawn players
	spawn_prophaunt_players()
	
	# Transition to InGame state
	server.set_state_to_prophaunt_ingame()

func load_prophaunt_map():
	"""Load the current Prophaunt map"""
	# Initialize map manager if not already done
	if not has_node("MapManager"):
		var map_manager = ProphauntMapManager.new()
		map_manager.name = "MapManager"
		add_child(map_manager)

	var map_manager = get_node("MapManager")
	var map_path = map_manager.get_next_map()
	print("Loading Prophaunt map: ", map_path)

	# Remove existing map if any
	if server.map:
		server.map.queue_free()

	# Load new map using map manager
	server.map = map_manager.load_map(map_path)
	if server.map:
		server.add_child(server.map)

		# Store map reference
		var map_packed = load(map_path) if ResourceLoader.exists(map_path) else null
		server.map_packed_scene = map_packed

		# Initialize network visibility if needed
		if server.network_visibility_manager:
			server.network_visibility_manager.load_all_network_nodes()
	else:
		print("Failed to load Prophaunt map: ", map_path)

func spawn_prophaunt_players():
	"""Spawn players in their appropriate locations"""
	var haunter_spawn_index = 0
	var prop_spawn_index = 0
	
	for key in server.players_data.keys():
		if server.is_bot(key) or server.is_dc(key):
			continue
		
		var player = server.players[key]
		if not player:
			continue
		
		if server.players_data[key]["prophaunt_team"] == Constants.ProphauntTeam.HAUNTERS:
			# Spawn haunters in haunter room
			var pos = get_haunter_spawn_position(haunter_spawn_index)
			var rot = get_haunter_spawn_rotation(haunter_spawn_index)
			player.global_position = pos
			player.global_rotation = rot

			# Initialize ProphauntPlayer component for haunter
			var player_manager = ProphauntPlayerManager.get_instance()
			player_manager.initialize_prophaunt_player(key, player, Constants.ProphauntTeam.HAUNTERS)

			haunter_spawn_index += 1
		else:
			# Spawn props randomly as objects in the map
			var pos = get_prop_spawn_position(prop_spawn_index)
			var rot = get_prop_spawn_rotation(prop_spawn_index)
			player.global_position = pos
			player.global_rotation = rot
			
			# Assign random prop disguise
			assign_random_prop_disguise(key)
			prop_spawn_index += 1
		
		# Update player data
		server.players_data[key]["server"]["checkpoint"] = player.global_position
		server.players_data[key]["d"]["p"] = player.global_position

func get_haunter_spawn_position(index):
	"""Get spawn position for haunters (in haunter room)"""
	var map_manager = get_node_or_null("MapManager")
	if map_manager:
		return map_manager.get_haunter_spawn_point(index)

	# Fallback positions
	var base_pos = Vector3(0, 1, -10)
	return base_pos + Vector3(index * 2, 0, 0)

func get_haunter_spawn_rotation(index):
	"""Get spawn rotation for haunters"""
	return Vector3(0, 0, 0)

func get_prop_spawn_position(index):
	"""Get spawn position for props (scattered around map)"""
	var map_manager = get_node_or_null("MapManager")
	if map_manager:
		return map_manager.get_prop_spawn_point(index)

	# Fallback positions
	var positions = [
		Vector3(5, 1, 5),
		Vector3(-5, 1, 5),
		Vector3(5, 1, -5),
		Vector3(-5, 1, -5),
		Vector3(10, 1, 0),
		Vector3(-10, 1, 0),
		Vector3(0, 1, 10),
		Vector3(0, 1, -10)
	]
	return positions[index % positions.size()]

func get_prop_spawn_rotation(index):
	"""Get spawn rotation for props"""
	return Vector3(0, randf() * TAU, 0)  # Random Y rotation

func assign_random_prop_disguise(player_id):
	"""Assign a random prop disguise to a prop player"""
	# Get available props from map manager
	var map_manager = get_node_or_null("MapManager")
	var prop_types = ["box", "barrel", "chair", "table", "plant"]  # Default

	if map_manager:
		var available_props = map_manager.get_available_prop_objects()
		if available_props.size() > 0:
			prop_types = available_props

	var selected_prop = prop_types[randi() % prop_types.size()]
	server.players_data[player_id]["prophaunt_disguise"] = selected_prop

	# Initialize ProphauntPlayer component
	var character = server.players[player_id]
	if character:
		var player_manager = ProphauntPlayerManager.get_instance()
		player_manager.initialize_prophaunt_player(player_id, character, Constants.ProphauntTeam.PROPS)
		player_manager.change_player_disguise(player_id, selected_prop)

	# Notify all players of the disguise
	for key in server.players_data.keys():
		if not server.is_bot(key) and not server.is_dc(key):
			multiplayer.rpc(key, ClientRPC, "prophaunt_player_disguised", [player_id, selected_prop])

func on_new_player(_players_data):
	lobby_timer += Constants.SERVER_PLAYER_JOINED_ADD_LOBBY_TIME
