extends Control
class_name Server

signal player_disconnected(player_id:int)

@onready var serverSyncV2: ServerSyncV2 = $ServerSyncV2


const PRINT_FPS = false
const FORCE_VERSION = 140
var state = Constants.ServerState.Lobby
var in_game_finished = false
var MAX_PLAYERS = 128
var peer = null
var players_count = 0

var in_game_timer = 0
var game_scene
var BOT_WAIT_REMOVE = 100#Fills in map request in game scene
var BOT_RESET_TIME = 30#Fills in map request in game scene

var LATENCY = false
var MIN_FAKE_LATENCY = 50
var MAX_FAKE_LATENCY = 100
var network_visibility_manager: NetworkVisibilityManager = null


var PLAYER_DEFAULT = {
	"backend_id": 0,
	"id": 0,
	"selection": {
		"name": "player",
		"character": "res://..."
	},
	"d": {
		"title": 255,
		"right_hand": 255,
		"vehicle": 255,
		"vehicle_sit": 0,
		"reserved": 255,
		"p": Vector3(-400, 5, 0), #position
		"a": 0, #animation
		"s": 0, #state
		"R": Vector3(), #rotation
		"v": Vector3(), #velocity
		"r": false, #ragdoll
		"c": { #controls
			"i": Vector2(0, 0),
			"j": false
		}
	},
	"server": {
		"checkpoint": Vector3(),
		"ready": false,
		"qualified": false,
		"eliminated": false,
		"observer": false,
	},
	"joined_time": 0,
	"authoritive": false,
}
var COSMETIC_DEFAULT = {
	"weapon": null
}
var players_data = {}
var players_meta_data = {} #For FPS and Ping
var players = {}
var players_cosmetic = {}
var client

var map_packed_scene
var map

var player_parent = null

var last_new_player_time = 0
var NEW_PLAYER_SYNC_TIME = 2500


func _ready():
	multiplayer.peer_connected.connect(self._player_connected)
	multiplayer.peer_disconnected.connect(self._player_disconnected)
	if Constants.is_server:
		create_server()


func create_server():
	peer = ENetMultiplayerPeer.new()
	var err = peer.create_server(Constants.SERVER_PORT, MAX_PLAYERS)
	if err != OK:
		print("Can't host, address in use. ", err)
		return
	peer.get_host().compress(ENetConnection.COMPRESS_RANGE_CODER)
	multiplayer.set_multiplayer_peer(peer)
	print("Server Created at PORT ", Constants.SERVER_PORT)
	set_start_state()


func _player_connected(_id):
	if Constants.is_server:
		print("player connected with id ", _id)


func _player_disconnected(_id):
	if Constants.is_server:
		player_disconnected.emit(_id)
		print("player disconnected with id ", _id)
		if players.has(_id):
			if state == Constants.ServerState.Lobby and StageManager.current_stage == 1:
				players[_id].queue_free()
				players.erase(_id)
			else:
				players[_id].disable()
		if players_data.has(_id):
			if state == Constants.ServerState.Lobby and StageManager.current_stage == 1:
				players_data.erase(_id)
				players_cosmetic.erase(_id)
				if Constants.game_mode == Constants.GameMode.Race:
					set_start_position_for_all()
			else:
				tag_eliminate(_id)
				tag_dc(_id)
		players_count = players_data.size()


func _physics_process(delta):
	if Constants.is_client():
		return
	
	if Constants.game_mode == Constants.GameMode.FreeRide:
		%InGame.run(delta)
		return
	elif Constants.game_mode == Constants.GameMode.Prophaunt:
		# Handle Prophaunt mode states
		if state == Constants.ServerState.Lobby:
			%ProphauntLobby.run(delta)
		elif state == Constants.ServerState.InGame:
			%ProphauntInGame.run(delta)
		elif state == Constants.ServerState.Results:
			%ProphauntResults.run(delta)
		return

	#print(players_data)
	if state == Constants.ServerState.Lobby:
		%Lobby.run(delta)
	if state == Constants.ServerState.CountDown:
		%CountDown.run(delta)
	if state == Constants.ServerState.InGame:
		%InGame.run(delta)
	if state == Constants.ServerState.Results:
		%Results.run(delta)


func restart_server():
	print("*********************")
	print("Restarting Server...")
	state = Constants.ServerState.Loading
	if StageManager.is_finished:
		StageManager.on_all_stages_finished() #Server
		return
	
	print("changing scene to decider")
	get_tree().change_scene_to_file("res://Scenes/decider_scene.tscn")
	game_scene.remove_child(self)
#############################################################################################
#########################################Received RPCs####################################
#############################################################################################


@rpc("any_peer", "call_remote", "reliable", Constants.CHANNEL_NEW_PLAYER)
func new_player(player_data):
	if Constants.is_client():
		return
	
	if state != Constants.ServerState.Lobby:
		return
	
	if Constants.game_mode == Constants.GameMode.Race:
		if players_count >= Constants.LOBBY_PLAYER_COUNT_START:
			print("too much players joined")
			multiplayer.multiplayer_peer.disconnect_peer(multiplayer.get_remote_sender_id())
			return
	
	if not player_data.has("version"):
		ClientRPC.kick.rpc_id(multiplayer.get_remote_sender_id())
		print("no version provided! kick!")
		return
	var version = player_data["version"]
	if version < FORCE_VERSION:
		ClientRPC.kick.rpc_id(multiplayer.get_remote_sender_id())
		print("older version client kick!")
		return
	var backend_id = player_data.get("backend_id", -1)
	if backend_id <= 0:
		ClientRPC.kick.rpc_id(multiplayer.get_remote_sender_id())
		print(player_data)
		print("Backend id error")
		return
	
	if Constants.SERVER_FORCE_NEW_PLAYER:
		if players_count >= MAX_PLAYERS:
			var is_admin = player_data.get("admin", false)
			if not is_admin:
				ClientRPC.kick_server_full.rpc_id(multiplayer.get_remote_sender_id())
				print("Kick: Server is full new player")
				return
	
	player_data["id"] = multiplayer.get_remote_sender_id()
	var id = player_data["id"]
	if find_player_data_by_id(id) != null:
		#Already added
		return
	
	%Lobby.on_new_player(player_data)
	
	
	var new_data = player_data.duplicate(true)
	players_data[id] = new_data
	players_data[id]["d"] = PLAYER_DEFAULT["d"].duplicate(true)
	players_data[id]["server"] = PLAYER_DEFAULT["server"].duplicate(true)
	players_data[id]["server"]["current_stage"] = true
	players_data[id]["joined_time"] = Time.get_ticks_msec()
	set_player_network_authorative(id, false)
	players_count = players_data.size()
	players_cosmetic[id] = COSMETIC_DEFAULT.duplicate(true)
	
	#Create player scene
	var path = new_data["selection"]["character"]
	var scene = null
	#print("new player character: ", path, " ", player_data)
	#if ResourceLoader.exists(path):
	scene = load(path)
	#else:
	#	scene = load(Selector.get_random_character_path())
	players[id] = scene.instantiate()
	players[id].name = str(id)
	player_parent.add_child(players[id])
	players[id].server = self
	players[id].make_remote()
	var pos = map.get_starting_position(players_count)
	@warning_ignore("shadowed_variable_base_class")
	var rotation = map.get_starting_rotation(players_count)
	players[id].global_position = pos
	players[id].global_rotation = rotation
	players[id].LOCAL_CHECKPOINT = pos #For Local Mode
	players_data[id]["server"]["checkpoint"] = pos
	players_data[id]["d"]["p"] = pos
	#print("new player at position ", players[id].global_position.x)
	last_new_player_time = Time.get_ticks_msec()
	
	if not is_syncv2():
		send_all_players_data(false, "sync_players_reliable")
	send_map_details(id)


@rpc("any_peer", "call_remote", "reliable", Constants.CHANNEL_NEW_PLAYER)
func reconnected(player_data, last_id):#Freeride
	if Constants.is_client():
		return
	
	if state != Constants.ServerState.Lobby:
		return
	
	if not player_data.has("version"):
		ClientRPC.kick.rpc_id(multiplayer.get_remote_sender_id())
		print("no version provided! kick!")
		return
	var version = player_data["version"]
	if version < FORCE_VERSION:
		ClientRPC.kick.rpc_id(multiplayer.get_remote_sender_id())
		print("older version client kick!")
		return
	
	if Constants.SERVER_FORCE_NEW_PLAYER:
		if players_count >= MAX_PLAYERS:
			var is_admin = player_data.get("admin", false)
			if not is_admin:
				ClientRPC.kick_server_full.rpc_id(multiplayer.get_remote_sender_id())
				print("Kick: Server is full on reconnect")
				return
	
	print("player reconnected: ", multiplayer.get_remote_sender_id(), " last=", last_id)
	if players.has(last_id):
		players[last_id].queue_free()
		players.erase(last_id)
	if players_data.has(last_id):
		players_data.erase(last_id)
		players_cosmetic.erase(last_id)
	
	player_data["id"] = multiplayer.get_remote_sender_id()
	var id = player_data["id"]
	if find_player_data_by_id(id) != null:
		#Already added
		return
	
	%Lobby.on_new_player(player_data)
	
	
	var new_data = player_data.duplicate(true)
	players_data[id] = new_data
	players_data[id]["d"] = PLAYER_DEFAULT["d"].duplicate(true)
	players_data[id]["server"] = PLAYER_DEFAULT["server"].duplicate(true)
	players_data[id]["server"]["current_stage"] = true
	players_data[id]["joined_time"] = Time.get_ticks_msec() - 1000 * 100#Avoid force sync
	set_player_network_authorative(id, false)
	players_count = players_data.size()
	
	#Create player scene
	var path = new_data["selection"]["character"]
	var scene = null
	scene = load(path)
	players[id] = scene.instantiate()
	players[id].name = str(id)
	player_parent.add_child(players[id])
	players[id].server = self
	players[id].make_remote()
	var pos = map.get_starting_position(players_count)
	players[id].LOCAL_CHECKPOINT = pos #For Local Mode
	players_data[id]["server"]["checkpoint"] = pos
	last_new_player_time = Time.get_ticks_msec()
	
	if not is_syncv2():
		send_all_players_data(false, "sync_players_reliable")


func new_bot_player(player_data):
	if state != Constants.ServerState.Lobby and not Constants.LOCAL_MODE:
		return

	player_data["id"] = randi() % 100000
	var id = player_data["id"]
	if find_player_data_by_id(id) != null:
		#Already added
		return
	
	%Lobby.on_new_player(player_data)
	
	var new_data = player_data.duplicate(true)
	players_data[id] = new_data
	players_data[id]["d"] = PLAYER_DEFAULT["d"].duplicate(true)
	players_data[id]["server"] = PLAYER_DEFAULT["server"].duplicate(true)
	players_data[id]["server"]["current_stage"] = true
	players_count = players_data.size()
	
	#Create player scene
	var path = new_data["selection"]["character"]
	var scene = null
	if ResourceLoader.exists(path):
		scene = load(path)
	else:
		scene = load(Selector.get_random_character_path())
	players[id] = scene.instantiate()
	players[id].name = str(id)
	player_parent.add_child(players[id])
	players[id].server = self
	players[id].repetive_bot_input_time = BOT_RESET_TIME
	players[id].make_bot()
	players[id].set_character_name(player_data["selection"]["name"])
	var pos = map.get_starting_position(players_count)
	@warning_ignore("shadowed_variable_base_class")
	var rotation = map.get_starting_rotation(players_count)
	if Constants.LOCAL_MODE:
		pos = map.get_starting_position(players_count + 1)
		rotation = map.get_starting_rotation(players_count + 1)
	players[id].global_position = pos
	players[id].global_rotation = rotation
	players[id].LOCAL_CHECKPOINT = pos #For Local Mode
	players_data[id]["server"]["checkpoint"] = pos
	players_data[id]["d"]["p"] = pos
	print("-new bot player")
	last_new_player_time = Time.get_ticks_msec()
	send_all_players_data(false, "sync_players_reliable")
	return id


@rpc("any_peer", "unreliable_ordered", "call_remote", Constants.CHANNEL_PLAYER_INPUT)
func update_player_data(packet):
	if state == Constants.ServerState.CountDown:
		return
	
	var id = multiplayer.get_remote_sender_id()
	var player_data = Constants.convert_packed_byte_array_to_my_controller(packet)
	if not players_data.has(id):
		return
	
	#Don't update vehicle data from client to prevent cheating
	var vehicle_data = players_data[id]["d"]["vehicle"]
	var sit_data = players_data[id]["d"]["vehicle_sit"]
	
	players_data[id]["d"] = player_data["d"]
	
	players_data[id]["d"]["vehicle"] = vehicle_data
	players_data[id]["d"]["vehicle_sit"] = sit_data
	
	
	var input = {
		"input_tick": player_data["input_tick"],
		"d": player_data["d"].duplicate(true),
	}
	if players_data[id]["authoritive"] == false:
		players[id].serverNetworkHistory.on_input_state_received(input)
	else:
		#Ignoring client data
		pass


@rpc("reliable", "any_peer", "call_remote", Constants.CHANNEL_PING)
func ping(ping_id, fps, _ping):
	if LATENCY:
		await get_tree().create_timer(randi_range(MIN_FAKE_LATENCY, MAX_FAKE_LATENCY) / 1000.0).timeout
	
	var id = multiplayer.get_remote_sender_id()
	if not players_meta_data.has(id):
		players_meta_data[id] = {
			"backend_id": players_data[id]["backend_id"],
			"fps": 0,
			"fps_count": 0,
			"ping": 0,
			"ping_count": 0,
		}
	if _ping != -1:
		#it's not first time!
		players_meta_data[id]["fps"] += fps
		players_meta_data[id]["fps_count"] += 1
		players_meta_data[id]["ping"] += _ping
		players_meta_data[id]["ping_count"] += 1
	if players_meta_data[id]["backend_id"] == null:
		players_meta_data[id]["backend_id"] = players_data[id]["backend_id"]
	
	if is_dc(id):
		return
	
	multiplayer.rpc(multiplayer.get_remote_sender_id(), ClientRPC, "pong", [ping_id])


@rpc("any_peer", "reliable", "call_remote", Constants.CHANNEL_PLAYER_JUMP)
func jump():
	var id = multiplayer.get_remote_sender_id()
	var data = players_data[id]["d"]
	data["c"]["j"] = true
	players_data[id]["d"] = data


@rpc("reliable", "any_peer", "call_remote", Constants.CHANNEL_LOBBY)
func iam_ready():
	var id = multiplayer.get_remote_sender_id()
	players_data[id]["server"]["ready"] = true


@rpc("any_peer", "call_remote", "reliable", Constants.CHANNEL_EMOTE)
func show_emote(emote_id):
	var id = multiplayer.get_remote_sender_id()
	var data = {
		"p": id,
		"e": emote_id,
		"t": Constants.EMOTE_SHOW_TIME,
	}
	for key in players_data.keys():
		if not is_bot(key):
			multiplayer.rpc(players_data[key]["id"], ClientRPC, "emote_player", [data])


@rpc("any_peer", "call_remote", "reliable", Constants.CHANNEL_EMOTE)
func show_hunger_alarm():
	var id = multiplayer.get_remote_sender_id()
	var emote_id = 60#Hunger Emote
	var sound_distance = 30 * 30
	var data = {
		"p": id,
		"e": emote_id,
		"t": Constants.EMOTE_SHOW_TIME,
	}
	for key in players_data.keys():
		if not is_bot(key):
			multiplayer.rpc(players_data[key]["id"], ClientRPC, "emote_player", [data])
			var pos = players[key].global_position as Vector3
			var sender_pos = players[id].global_position
			if (pos - sender_pos).length_squared() <= sound_distance:
				SoundManager.play_sound.rpc_id(key, SoundManager.SOUND_TYPE.HUNGER)


@rpc("any_peer", "call_remote", "reliable", Constants.CHANNEL_EMOTE)
func start_emote(emote_id):
	var id = multiplayer.get_remote_sender_id()
	var data = {
		"p": id,
		"e": emote_id,
		"t": 3600 * 4,
	}
	for key in players_data.keys():
		if not is_bot(key):
			multiplayer.rpc(players_data[key]["id"], ClientRPC, "emote_player", [data])


@rpc("any_peer", "call_remote", "reliable", Constants.CHANNEL_EMOTE)
func end_emote(emote_id):
	var id = multiplayer.get_remote_sender_id()
	var data = {
		"p": id,
		"e": emote_id,
		"t": 0,
	}
	for key in players_data.keys():
		if not is_bot(key):
			#print("send end emote to : ", players_data[key]["id"], " data=", data)
			multiplayer.rpc(players_data[key]["id"], ClientRPC, "emote_player", [data])


@rpc("any_peer", "call_remote", "reliable", Constants.CHANNEL_CHANGE_CHARACTER)
func change_character(player_data):
	if Constants.is_client():
		return

	player_data["id"] = multiplayer.get_remote_sender_id()
	var id = player_data["id"]
	if find_player_data_by_id(id) == null:
		print("Change Character RPC: Player not found: ", id)
		return

	#Change player scene
	players_data[id]["selection"]["character"] = player_data["character"]
	var path = player_data["character"]
	var scene = null
	scene = load(path)
	var previous_scene = players[id]
	previous_scene.disable()
	players[id] = scene.instantiate()
	player_parent.add_child(players[id])
	players[id].server = self
	players[id].make_remote()
	players[id].global_position = previous_scene.global_position
	players[id].global_rotation = previous_scene.global_rotation
	players[id].LOCAL_CHECKPOINT = Vector3(0, 0, 0) #For Local Mode
	last_new_player_time = Time.get_ticks_msec()

	for key in players_data.keys():
		if is_bot(key):
			continue
		if is_dc(key):
			continue
		multiplayer.rpc(key, ClientRPC, "character_changed", [player_data])


	await get_tree().process_frame
	await get_tree().process_frame
	previous_scene.queue_free()


@rpc("any_peer", "call_remote", "reliable", Constants.CHANNEL_CHANGE_CHARACTER)
func change_character_name(handle):
	if Constants.is_client():
		return

	var id = multiplayer.get_remote_sender_id()
	if find_player_data_by_id(id) == null:
		print_debug("Change Character Name RPC: Player not found: ", id)
		return

	players_data[id]["selection"]["name"] = handle

	var data = {
		"id": id,
		"handle": handle
	}
	for key in players_data.keys():
		if is_bot(key):
			continue
		if is_dc(key):
			continue
		multiplayer.rpc(key, ClientRPC, "character_name_changed", [data])


@rpc("any_peer", "call_remote", "reliable", Constants.CHANNEL_GUN)
func gunshot(start_ray, end_ray, damage):
	if not Constants.SERVER_GUN_ALLOW:
		print("no gun allowed")
		return
	var id = multiplayer.get_remote_sender_id()
	var verifier = GunshotVerifier.new()
	add_child(verifier)
	verifier.sender_backend_id = players_data[id]["backend_id"]
	verifier.sender_remote_id = multiplayer.get_remote_sender_id()
	verifier.start_ray = start_ray
	verifier.end_ray = end_ray
	verifier.damage = damage
	
	verifier.send_request()


@rpc("any_peer", "call_remote", "reliable", Constants.CHANNEL_GUN)
func melee_attack(_range, damage):
	if not Constants.SERVER_GUN_ALLOW:
		print("no gun allowed")
		return
	var id = multiplayer.get_remote_sender_id()
	
	var verifier = BatonVerifier.new()
	add_child(verifier)
	verifier.damage = damage
	verifier._range = _range
	verifier.sender_backend_id = players_data[id]["backend_id"]
	verifier.sender_remote_id = multiplayer.get_remote_sender_id()
	verifier.police_pos = players[id].global_position
	verifier.send_request()

#############################################################################################
#########################################Send RPCs###########################################
#############################################################################################
var individual_sync_counter = 0
func handle_individual_sync(delta):
	individual_sync_counter += delta
	if individual_sync_counter >= Constants.SERVER_MY_PLAYER_SYNC:
		send_individual_players_data()
		individual_sync_counter = 0


var send_all_players_counter = 0
func handle_send_all_players_data(delta):
	send_all_players_counter += delta
	if send_all_players_counter >= Constants.SERVER_ALL_PLAYERS_SYNC:
		send_all_players_counter = 0
		if Time.get_ticks_msec() - last_new_player_time < NEW_PLAYER_SYNC_TIME:
			send_all_players_data(false, "sync_players_unreliable")
		else:
			send_all_players_data(true, "sync_players_unreliable")


func send_individual_players_data():
	if Constants.is_client():
		return
	
	for key in players_data.keys():
		if is_bot(key):
			continue
		if players_data[key]["server"]["eliminated"] == true:
			continue
		
		var data
		var tick
		if players[key].serverNetworkHistory.last_handled_tick == -1:
			data = players_data[key]
			tick = TickManager.tick_counter
		else:
			data = players[key].serverNetworkHistory.get_last_handled_state()
			data["id"] = players_data[key]["id"]
			tick = players[key].serverNetworkHistory.last_handled_tick
		var packed = Constants.convert_player_data_to_packed_byte_array(data, tick)
		
		if LATENCY:
			await get_tree().create_timer(randi_range(MIN_FAKE_LATENCY, MAX_FAKE_LATENCY) / 1000.0).timeout
		
		if is_dc(key):
			continue
		if Constants.game_mode == Constants.GameMode.Race:
			multiplayer.rpc(data["id"], ClientRPC, "sync_my_data", [packed])
			continue

		if get_players_joined_time(key) > Constants.PLAYER_FORCE_STATE_SYNC_TIME * 1000:
			multiplayer.rpc(data["id"], ClientRPC, "sync_my_data", [packed])
		else:
			multiplayer.rpc(data["id"], ClientRPC, "force_sync_my_data", [packed])


func send_all_players_data(trim=true, _rpc="sync_players_unreliable"):
	if Constants.is_client():
		return
	
	var trimed_data = players_data.duplicate(true)
	if trim:
		for key in trimed_data.keys():
			trimed_data[key].erase("selection")
			trimed_data[key].erase("backend_id")
	
	var packed_data = {}
	for key in players_data.keys():
		if players_data[key]["server"]["eliminated"] == true:
			continue
		packed_data[key] = Constants.convert_player_data_to_packed_byte_array(trimed_data[key])
	
	for key in players_data.keys():
		if is_bot(key):
			continue
		var data = players_data[key]
		if is_dc(data["id"]):
			continue
		multiplayer.rpc(data["id"], ClientRPC, _rpc, [packed_data])


var in_game_timer_counter = 0
func handle_in_game_timer(delta):
	if Constants.game_mode == Constants.GameMode.FreeRide:
		return
	in_game_timer_counter += delta
	if in_game_timer_counter >= Constants.SERVER_IN_GAME_TIMER_SYNC:
		in_game_timer_counter = 0
		for key in players_data.keys():
			if is_bot(key):
				continue
			if is_dc(key):
				continue
			multiplayer.rpc(key, ClientRPC, "ingame_timer", [Constants.SERVER_IN_GAME_TIME - in_game_timer])


#Calls from GameSettings after reaching FinishLine
func player_qualified(player_id):
	players[player_id].disable()
	players_data[player_id]["server"]["qualified"] = true
	for key in players_data.keys():
		if is_bot(key):
			continue
		var data = players_data[key]
		if is_dc(data["id"]):
			continue
		multiplayer.rpc(data["id"], ClientRPC, "update_result", [player_id])


func player_eliminated_in_elimination_map(player_id):
	for key in players_data.keys():
		if is_bot(key):
			continue
		var data = players_data[key]
		if is_dc(data["id"]):
			continue
		multiplayer.rpc(data["id"], ClientRPC, "update_result", [player_id])


func send_map_details(player_id):
	if is_bot(player_id):
		return
	if is_dc(player_id):
		return
	var map_data = {
		"resource": map_packed_scene.resource_path
	}
	multiplayer.rpc(player_id, ClientRPC, "map_details", [map_data])
#############################################################################################
#########################################State Funcs#########################################
#############################################################################################
func finish_in_game():
	print("Finishing in game!")
	in_game_finished_counter = 0


var in_game_finished_counter = 0
func in_game_finished_process(delta):
	var qualified_list = [GameSettings.backend_container_id] #First element is backend id rest is qualified
	
	if map.type == Constants.MapType.Qualification:
		for key in players_data.keys():
			if players_data[key]["server"]["qualified"] == true:
				qualified_list.append(key)
			else:
				players_data[key]["server"]["qualified"] = false
				players_data[key]["server"]["eliminated"] = true
	
	if map.type == Constants.MapType.Elimination:
		for key in players_data.keys():
			if players_data[key]["server"]["eliminated"] == false:
				qualified_list.append(key)
			else:
				players_data[key]["server"]["qualified"] = false
				players_data[key]["server"]["eliminated"] = true
	
	in_game_finished_counter += delta
	if in_game_finished_counter >= Constants.IN_GAME_FINISHED_TIME:
		state = Constants.ServerState.Results
		%Results.set_state()
		StageManager.on_stage_finished(map.type)
		GameSettings.players_meta_data = players_meta_data.duplicate(true)
		print("Set Server state to Results!")
		for key in players_data.keys():
			if is_bot(key):
				continue
			var data = players_data[key]
			if is_dc(data["id"]):
				continue
			multiplayer.rpc(data["id"], ClientRPC, "map_finished", [qualified_list])
		StageManager.set_server_qualified(qualified_list)
		if map.type == Constants.MapType.Elimination:
			GameSettings.calculate_qualified()
		BackendManager.send_finish_game_request(map.type)
		StageManager.backup_server_data(self, player_parent)


func set_state_to_countdown():
	%CountDown.set_state()


func set_start_state():
	print("Set State to LOADING ON START")
	if peer:
		peer.refuse_new_connections = false
	state = Constants.ServerState.Loading


func set_state_to_lobby():
	if Constants.game_mode == Constants.GameMode.Prophaunt:
		%ProphauntLobby.set_state()
	else:
		%Lobby.set_state()


func start_the_game():
	print("Game starts")
	#Set starting location
	set_start_position_for_all()


func set_state_to_in_game():
	%InGame.set_state()


# Prophaunt state transition functions
func set_state_to_prophaunt_lobby():
	%ProphauntLobby.set_state()


func set_state_to_prophaunt_ingame():
	%ProphauntInGame.set_state()


func set_state_to_prophaunt_results():
	%ProphauntResults.set_state()


func on_new_game():
	in_game_timer = 0
	in_game_timer_counter = 0
	in_game_finished = false


func init_stage():
	if StageManager.current_stage == 1:
		return
	
	on_new_game()

	print("INIT STAGE")
	players_data = StageManager.server_players_data
	players = StageManager.server_players

	var index = 1
	for key in players.keys():
		var player = players[key]
		if is_instance_valid(player):
			StageManager.remove_child(player)
			player_parent.add_child(player)
		
		var condition = false
		if StageManager.last_stage_map_type == Constants.MapType.Qualification:
			condition = players_data[key]["server"]["eliminated"] == true or players_data[key]["server"]["qualified"] == false
		if StageManager.last_stage_map_type == Constants.MapType.Elimination:
			condition = players_data[key]["server"]["eliminated"] == true
		
		if condition:
			players_data[key]["server"]["current_stage"] = false
			player.disable()
		else:
			var start_position = map.get_starting_position(index)
			var start_rotation = map.get_starting_rotation(index)
			player.enable()
			player.global_position = start_position
			player.global_rotation = start_rotation
			player.LOCAL_CHECKPOINT = start_position #For Local Mode
			player.repetive_bot_input_counter = 0 #Reset bot timer counter
			players_data[key]["server"]["checkpoint"] = start_position
			players_data[key]["d"]["p"] = start_position
			players_data[key]["server"]["qualified"] = false
			players_data[key]["server"]["eliminated"] = false
			players_data[key]["server"]["current_stage"] = true
			index += 1
		players_meta_data[key] = {
			"backend_id": players_data[key]["backend_id"],
			"fps": 0,
			"fps_count": 0,
			"ping": 0,
			"ping_count": 0,
		}
		send_map_details(key)
#############################################################################################
#########################################GamePlay Funcs######################################
#############################################################################################
func checkpoint(player_id, _position):
	if Constants.is_client():
		return
	players_data[player_id]["server"]["checkpoint"] = _position


func lose_area(player_id):
	if Constants.is_client():
		return
	players[player_id].global_position = players_data[player_id]["server"]["checkpoint"]
	players[player_id].ragdoll = false


#############################################################################################
#########################################Utils###############################################
#############################################################################################
func count_players_ready():
	if players_data == null:
		return 0

	var count = 0
	for key in players_data.keys():
		if is_bot(key):
			continue
		if players_data[key]["server"]["ready"] == true:
			count += 1
			
	return count


func is_all_players_ready():
	if players_data == null:
		return false

	for key in players_data.keys():
		if is_bot(key):
			continue
		if players_data[key]["server"]["ready"] == false:
			return false
	return true


func set_start_position_for_all():
	var count = 1
	for key in players_data.keys():
		if StageManager.current_stage > 1:
			if players_data[key]["server"]["eliminated"] == true or players_data[key]["server"]["qualified"] == false:
				continue
		
		var pos = map.get_starting_position(count)
		@warning_ignore("shadowed_variable_base_class")
		var rotation = map.get_starting_rotation(count)
		players[key].global_position = pos
		players[key].global_rotation = rotation
		players_data[key]["server"]["checkpoint"] = pos
		players_data[key]["d"]["p"] = pos
		count += 1


func is_bot(player_id):
	if not players.has(player_id):
		return true
	return players[player_id].is_bot()


func count_non_bot_players():
	var count = 0
	for key in players_data.keys():
		if not is_bot(key):
			count += 1
	return count


func count_bot_players():
	var count = 0
	for key in players_data.keys():
		if is_bot(key):
			count += 1
	return count


func find_player_data_by_id(id):
	if players_data.has(id):
		return players_data[id]
	return null


func get_player_scene_by_id(id):
	if players.has(id):
		return players[id]
	return null


func set_all_player_scenes_data():
	for key in players.keys():
		players[key].server_player_data = players_data[key]["d"]
		players[key].player_id = key


func update_players_data():
	if Constants.is_client():
		return
	for key in players_data.keys():
		if players_data[key]["server"]["eliminated"] == true:
			continue
		var p = players[key]
		var data = players_data[key]["d"]
		#if is_bot(key):
		data["p"] = p.global_position
		data["R"] = p.global_rotation
		data["a"] = p.get_current_animation()
		data["s"] = p.state
		data["r"] = p.ragdoll
		data["v"] = p.velocity
		
		players_data[key]["d"] = data


func check_y_of_players():
	if Constants.game_mode == Constants.GameMode.FreeRide:
		return
	if map.type == Constants.MapType.Elimination:
		return
	for key in players.keys():
		if is_dc(key) or is_eliminated(key) or is_qualified(key):
			continue
		if players[key].global_position.y < -50:
			#print(key, " lose area check y")
			players[key].lose_area() #For bots and Client!!!
			


func tag_eliminate(key):
	if Constants.game_mode == Constants.GameMode.FreeRide:
		return
	if is_eliminated(key):
		return
	players_data[key]["server"]["eliminated"] = true
	players_data[key]["server"]["qualified"] = false
	
	if map.type == Constants.MapType.Elimination:
		print("Player Eliminated: ", key, " ", GameSettings.eliminated_count(), "/", GameSettings.qualified_game_finished)
		player_eliminated_in_elimination_map(key)


func tag_dc(key):
	if not players_data[key].has("server"):
		players_data[key]["server"] = {}
	players_data[key]["server"]["dc"] = true


func is_dc(key):
	if key == null:
		return true

	if not players_data.has(key):
		return true
	if not players_data[key].has("server"):
		return false
	
	if not players_data[key]["server"].has("dc"):
		return false
	
	return players_data[key]["server"]["dc"]


func is_eliminated(key):
	if not players_data[key].has("server"):
		return false
	
	if not players_data[key]["server"].has("eliminated"):
		return false
	
	return players_data[key]["server"]["eliminated"]


func is_qualified(key):
	if not players_data[key].has("server"):
		return false
	
	if not players_data[key]["server"].has("qualified"):
		return false
	
	return players_data[key]["server"]["qualified"]


func get_players_joined_time(key):
	if players_data[key].has("joined_time"):
		return Time.get_ticks_msec() - players_data[key]["joined_time"]
	
	return Time.get_ticks_msec()


func set_player_network_authorative(id, auth_bool:bool, send_rpc=false) -> void:
	if not players_data.has(id):
		return
	players_data[id]["authoritive"] = auth_bool
	if send_rpc:
		ClientRPC.rpc_id(id, "set_my_authoritive", auth_bool)


func is_syncv2():
	return (Constants.game_mode == Constants.GameMode.FreeRide) and Constants.server_sync_v2
