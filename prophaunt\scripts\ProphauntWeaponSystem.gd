extends Node
class_name ProphauntWeaponSystem

# Prophaunt weapon system for Haunters
# Handles shooting, grenades, and weapon effects

var character: Character
var prophaunt_player: ProphauntPlayer

# Weapon stats
var gun_damage = Constants.PROPHAUNT_GUN_DAMAGE
var gun_range = 50.0
var gun_cooldown = 1.0
var gun_last_shot = 0.0

var grenade_damage = Constants.PROPHAUNT_GRENADE_DAMAGE
var grenade_radius = Constants.PROPHAUNT_GRENADE_RADIUS
var grenade_cooldown = 10.0
var grenade_last_thrown = 0.0

# Visual effects
var muzzle_flash_scene = preload("res://assets/VFX/MuzzleFlash.tscn") # Placeholder
var explosion_scene = preload("res://assets/VFX/Explosion.tscn") # Placeholder

signal weapon_fired(position: Vector3, direction: Vector3)
signal grenade_thrown(position: Vector3, target: Vector3)
signal target_hit(target_id: int, damage: int)

func _ready():
	pass

func initialize(player_character: Character, prophaunt_comp: ProphauntPlayer):
	"""Initialize the weapon system"""
	character = player_character
	prophaunt_player = prophaunt_comp

func can_shoot() -> bool:
	"""Check if the player can shoot"""
	if not prophaunt_player:
		return false
	
	if prophaunt_player.team != Constants.ProphauntTeam.HAUNTERS:
		return false
	
	if prophaunt_player.player_state == Constants.ProphauntPlayerState.HEXED:
		return false
	
	var current_time = Time.get_ticks_msec() / 1000.0
	return current_time - gun_last_shot >= gun_cooldown

func can_throw_grenade() -> bool:
	"""Check if the player can throw a grenade"""
	if not prophaunt_player:
		return false
	
	if prophaunt_player.team != Constants.ProphauntTeam.HAUNTERS:
		return false
	
	if prophaunt_player.player_state == Constants.ProphauntPlayerState.HEXED:
		return false
	
	var current_time = Time.get_ticks_msec() / 1000.0
	return current_time - grenade_last_thrown >= grenade_cooldown

func shoot(target_position: Vector3) -> bool:
	"""Shoot at target position"""
	if not can_shoot():
		return false
	
	gun_last_shot = Time.get_ticks_msec() / 1000.0
	
	# Get shooting origin
	var shoot_origin = get_shoot_origin()
	var shoot_direction = (target_position - shoot_origin).normalized()
	
	# Perform raycast to check what was hit
	var hit_result = perform_shoot_raycast(shoot_origin, target_position)
	
	# Create visual effects
	create_muzzle_flash(shoot_origin, shoot_direction)
	create_bullet_trail(shoot_origin, hit_result.hit_position)
	
	# Play sound
	SoundManager.play_3d_sound.rpc_id(1, shoot_origin, gun_range, SoundManager.SOUND_TYPE.Pistol)
	
	# Handle hit
	if hit_result.hit_player_id != -1:
		handle_player_hit(hit_result.hit_player_id, hit_result.hit_position)
	else:
		handle_object_hit(hit_result.hit_position)
	
	weapon_fired.emit(shoot_origin, shoot_direction)
	return true

func throw_grenade(target_position: Vector3) -> bool:
	"""Throw grenade at target position"""
	if not can_throw_grenade():
		return false
	
	grenade_last_thrown = Time.get_ticks_msec() / 1000.0
	
	# Get throw origin
	var throw_origin = get_throw_origin()
	
	# Create grenade projectile
	create_grenade_projectile(throw_origin, target_position)
	
	# Play sound
	SoundManager.play_3d_sound.rpc_id(1, throw_origin, 20, SoundManager.SOUND_TYPE.MissleWhistle)
	
	grenade_thrown.emit(throw_origin, target_position)
	return true

func get_shoot_origin() -> Vector3:
	"""Get the position to shoot from"""
	if character and character.has_node("AttackPlaceHolder"):
		return character.get_node("AttackPlaceHolder").global_position
	elif character:
		return character.global_position + Vector3(0, 1.5, 0)  # Eye level
	return Vector3.ZERO

func get_throw_origin() -> Vector3:
	"""Get the position to throw from"""
	if character and character.has_node("ThrowPlaceHolder"):
		return character.get_node("ThrowPlaceHolder").global_position
	elif character:
		return character.global_position + Vector3(0, 1.5, 0)  # Eye level
	return Vector3.ZERO

func perform_shoot_raycast(origin: Vector3, target: Vector3) -> Dictionary:
	"""Perform raycast to determine what was hit"""
	var result = {
		"hit_player_id": -1,
		"hit_position": target,
		"hit_object": null
	}
	
	if not character:
		return result
	
	var space_state = character.get_world_3d().direct_space_state
	var query = PhysicsRayQueryParameters3D.create(origin, target)
	query.exclude = [character]  # Don't hit the shooter
	
	var hit = space_state.intersect_ray(query)
	
	if hit:
		result.hit_position = hit.position
		
		# Check if we hit a player
		var hit_body = hit.collider
		if hit_body and hit_body.has_method("get_player_id"):
			result.hit_player_id = hit_body.get_player_id()
		elif hit_body and hit_body.get_parent() and hit_body.get_parent().has_method("get_player_id"):
			result.hit_player_id = hit_body.get_parent().get_player_id()
		else:
			result.hit_object = hit_body
	
	return result

func handle_player_hit(player_id: int, hit_position: Vector3):
	"""Handle hitting a player"""
	# Check if the hit player is a prop
	var player_manager = ProphauntPlayerManager.get_instance()
	var hit_prophaunt_player = player_manager.get_prophaunt_player(player_id)
	
	if hit_prophaunt_player and hit_prophaunt_player.team == Constants.ProphauntTeam.PROPS:
		# Hit a prop - deal damage
		if Constants.is_client():
			multiplayer.rpc_id(1, "prophaunt_shoot", hit_position, gun_damage)
		target_hit.emit(player_id, gun_damage)
		
		# Create hit effect
		create_hit_effect(hit_position, true)
	else:
		# Hit a haunter or invalid target - no damage
		create_hit_effect(hit_position, false)

func handle_object_hit(hit_position: Vector3):
	"""Handle hitting a non-player object"""
	# Damage the shooter for hitting wrong target
	if Constants.is_client():
		var self_damage = Constants.PROPHAUNT_SELF_DAMAGE
		prophaunt_player.take_damage(self_damage)
	
	# Create hit effect
	create_hit_effect(hit_position, false)

func create_muzzle_flash(position: Vector3, direction: Vector3):
	"""Create muzzle flash effect"""
	# This would create a visual muzzle flash effect
	# For now, we'll just print debug info
	print("Muzzle flash at: ", position, " direction: ", direction)

func create_bullet_trail(start: Vector3, end: Vector3):
	"""Create bullet trail effect"""
	# This would create a visual bullet trail
	print("Bullet trail from: ", start, " to: ", end)

func create_hit_effect(position: Vector3, is_valid_hit: bool):
	"""Create hit effect"""
	# Different effects for valid vs invalid hits
	if is_valid_hit:
		print("Valid hit effect at: ", position)
		# Create blood/damage effect
	else:
		print("Invalid hit effect at: ", position)
		# Create spark/ricochet effect

func create_grenade_projectile(origin: Vector3, target: Vector3):
	"""Create and launch grenade projectile"""
	# Calculate trajectory
	var direction = (target - origin).normalized()
	var distance = origin.distance_to(target)
	var throw_force = min(distance * 2, 30.0)  # Cap the force
	
	# Create grenade (this would be a RigidBody3D in a full implementation)
	print("Grenade thrown from: ", origin, " to: ", target, " with force: ", throw_force)
	
	# Schedule explosion
	var flight_time = distance / 20.0  # Approximate flight time
	get_tree().create_timer(flight_time).timeout.connect(explode_grenade.bind(target))

func explode_grenade(position: Vector3):
	"""Explode grenade at position"""
	print("Grenade exploded at: ", position)
	
	# Create explosion effect
	create_explosion_effect(position)
	
	# Play explosion sound
	SoundManager.play_3d_sound.rpc_id(1, position, 25, SoundManager.SOUND_TYPE.ExplosionMedium)
	
	# Send grenade damage to server
	if Constants.is_client():
		multiplayer.rpc_id(1, "prophaunt_grenade", position)

func create_explosion_effect(position: Vector3):
	"""Create explosion visual effect"""
	print("Explosion effect at: ", position)
	# This would create particle effects, screen shake, etc.

# Input handling
func handle_shoot_input():
	"""Handle shoot input"""
	if not character or not character.is_me():
		return
	
	if Input.is_action_just_pressed("shoot"):
		var target_pos = get_aim_target()
		shoot(target_pos)

func handle_grenade_input():
	"""Handle grenade input"""
	if not character or not character.is_me():
		return
	
	if Input.is_action_just_pressed("grenade"):
		var target_pos = get_grenade_target()
		throw_grenade(target_pos)

func get_aim_target() -> Vector3:
	"""Get the position the player is aiming at"""
	if not character:
		return Vector3.ZERO
	
	# Use camera to determine aim target
	var camera = character.get_viewport().get_camera_3d()
	if camera:
		var mouse_pos = get_viewport().get_mouse_position()
		var from = camera.project_ray_origin(mouse_pos)
		var to = from + camera.project_ray_normal(mouse_pos) * gun_range
		return to
	
	# Fallback: aim forward
	return character.global_position + character.global_transform.basis.z * -gun_range

func get_grenade_target() -> Vector3:
	"""Get the target position for grenade"""
	# Similar to aim target but could have different logic
	return get_aim_target()

func _input(event):
	"""Handle weapon input"""
	if Constants.game_mode != Constants.GameMode.Prophaunt:
		return
	
	if not prophaunt_player or prophaunt_player.team != Constants.ProphauntTeam.HAUNTERS:
		return
	
	handle_shoot_input()
	handle_grenade_input()

# Utility functions
func get_gun_cooldown_remaining() -> float:
	"""Get remaining gun cooldown"""
	var current_time = Time.get_ticks_msec() / 1000.0
	var remaining = gun_cooldown - (current_time - gun_last_shot)
	return max(0, remaining)

func get_grenade_cooldown_remaining() -> float:
	"""Get remaining grenade cooldown"""
	var current_time = Time.get_ticks_msec() / 1000.0
	var remaining = grenade_cooldown - (current_time - grenade_last_thrown)
	return max(0, remaining)
