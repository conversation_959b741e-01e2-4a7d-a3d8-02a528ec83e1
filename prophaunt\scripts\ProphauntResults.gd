extends ProphauntServerState

var results_timer = 0.0
var results_sent = false

func set_state():
	print("Set State to Prophaunt Results")
	server.state = Constants.ServerState.Results
	results_timer = 0.0
	results_sent = false

func run(delta):
	if not server:
		return
	
	results_timer += delta
	
	# Send results to players if not already sent
	if not results_sent:
		send_final_results()
		results_sent = true
	
	# After showing results for 15 seconds, start next round
	if results_timer >= 15.0:
		start_next_round()

func send_final_results():
	"""Send final results to all players"""
	var final_results = calculate_final_results()
	
	for key in server.players_data.keys():
		if server.is_bot(key) or server.is_dc(key):
			continue
		multiplayer.rpc(key, ClientRPC, "prophaunt_final_results", [final_results])

func calculate_final_results():
	"""Calculate comprehensive results for the completed rounds"""
	var results = {
		"total_rounds": current_round - 1,
		"rounds_on_current_map": rounds_played_on_map,
		"leaderboard": [],
		"team_stats": {
			"props_wins": 0,
			"haunters_wins": 0
		}
	}
	
	# Calculate player scores and create leaderboard
	var player_scores = {}
	
	for key in server.players_data.keys():
		if server.is_bot(key) or server.is_dc(key):
			continue
		
		# Initialize player score data
		player_scores[key] = {
			"player_id": key,
			"name": server.players_data[key]["selection"]["name"],
			"total_score": 0,
			"rounds_survived": 0,
			"rounds_as_prop": 0,
			"rounds_as_haunter": 0,
			"team": server.players_data[key]["prophaunt_team"]
		}
	
	# Create leaderboard sorted by score
	var leaderboard_array = []
	for player_data in player_scores.values():
		leaderboard_array.append(player_data)
	
	leaderboard_array.sort_custom(func(a, b): return a["total_score"] > b["total_score"])
	results["leaderboard"] = leaderboard_array
	
	return results

func start_next_round():
	"""Start the next round or end the game"""
	# Check if we should continue with more rounds
	if should_continue_game():
		# Reset for next round
		reset_for_next_round()
		server.set_state_to_prophaunt_lobby()
	else:
		# End the game session
		end_game_session()

func should_continue_game():
	"""Determine if the game should continue with more rounds"""
	# Continue if we have enough players and haven't reached max rounds
	var active_players = 0
	for key in server.players_data.keys():
		if not server.is_bot(key) and not server.is_dc(key):
			active_players += 1
	
	return active_players >= 2  # Minimum players to continue

func reset_for_next_round():
	"""Reset game state for the next round"""
	# Clear team assignments (will be reassigned in lobby)
	props_team.clear()
	haunters_team.clear()
	props_alive.clear()
	
	# Reset player states
	for key in server.players_data.keys():
		if server.is_bot(key) or server.is_dc(key):
			continue
		
		server.players_data[key]["prophaunt_hp"] = Constants.PROPHAUNT_PROP_DEFAULT_HP
		server.players_data[key]["prophaunt_state"] = Constants.ProphauntPlayerState.ALIVE
		server.players_data[key]["prophaunt_hex_cooldown"] = 0.0
		server.players_data[key]["prophaunt_sound_cooldown"] = Constants.PROPHAUNT_PROP_SOUND_COOLDOWN
		server.players_data[key]["prophaunt_disguise"] = ""
		server.players_data[key]["prophaunt_team"] = -1

func end_game_session():
	"""End the current game session"""
	print("Ending Prophaunt game session")
	
	# Send final goodbye message to players
	for key in server.players_data.keys():
		if server.is_bot(key) or server.is_dc(key):
			continue
		multiplayer.rpc(key, ClientRPC, "prophaunt_game_ended", [])
	
	# Reset server state
	current_round = 1
	rounds_played_on_map = 0
	
	# Transition back to lobby or shutdown
	server.set_state_to_prophaunt_lobby()
