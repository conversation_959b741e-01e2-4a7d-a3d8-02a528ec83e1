extends Node
class_name ProphauntMapManager

# Prophaunt map management system
# Handles map loading, prop placement, and map rotation

# Map configuration
var available_maps: Array = []
var current_map_index: int = 0
var current_map: Node = null
var map_rotation_enabled: bool = true

# Prop placement data
var prop_spawn_points: Array = []
var haunter_spawn_points: Array = []
var available_prop_objects: Array = []

# Map metadata
var map_metadata: Dictionary = {}

signal map_loaded(map_name: String)
signal map_unloaded(map_name: String)
signal prop_spawn_points_generated(points: Array)
signal haunter_spawn_points_generated(points: Array)

func _ready():
	initialize_map_system()

func initialize_map_system():
	"""Initialize the map management system"""
	load_map_configuration()
	scan_for_available_maps()
	print("ProphauntMapManager initialized with ", available_maps.size(), " maps")

func load_map_configuration():
	"""Load map configuration from file or define defaults"""
	# Default map configuration
	map_metadata = {
		"warehouse": {
			"name": "Warehouse",
			"description": "A large warehouse with boxes and machinery",
			"max_players": 12,
			"recommended_props": ["box", "barrel", "crate", "machinery"],
			"spawn_areas": {
				"haunters": Vector3(0, 1, -20),
				"props_center": Vector3(0, 1, 10)
			}
		},
		"office": {
			"name": "Office Building",
			"description": "A multi-floor office with desks and equipment",
			"max_players": 10,
			"recommended_props": ["chair", "desk", "computer", "plant", "trash_can"],
			"spawn_areas": {
				"haunters": Vector3(-15, 1, 0),
				"props_center": Vector3(15, 1, 0)
			}
		},
		"house": {
			"name": "Suburban House",
			"description": "A cozy house with furniture and decorations",
			"max_players": 8,
			"recommended_props": ["chair", "table", "lamp", "plant", "tv"],
			"spawn_areas": {
				"haunters": Vector3(0, 1, -10),
				"props_center": Vector3(0, 1, 5)
			}
		}
	}

func scan_for_available_maps():
	"""Scan for available Prophaunt maps"""
	available_maps.clear()
	
	# Add default maps
	var default_maps = [
		"res://prophaunt/maps/warehouse_map.tscn",
		"res://prophaunt/maps/office_map.tscn",
		"res://prophaunt/maps/house_map.tscn"
	]
	
	for map_path in default_maps:
		if ResourceLoader.exists(map_path):
			available_maps.append(map_path)
		else:
			# Create placeholder entry for development
			available_maps.append(map_path)
			print("Map not found (will use placeholder): ", map_path)
	
	# Scan prophaunt/maps directory for additional maps
	var dir = DirAccess.open("res://prophaunt/maps/")
	if dir:
		dir.list_dir_begin()
		var file_name = dir.get_next()
		
		while file_name != "":
			if file_name.ends_with(".tscn") and not file_name in available_maps:
				var full_path = "res://prophaunt/maps/" + file_name
				available_maps.append(full_path)
			file_name = dir.get_next()

func get_next_map() -> String:
	"""Get the next map in rotation"""
	if available_maps.size() == 0:
		return create_default_map()
	
	var map_path = available_maps[current_map_index]
	
	if map_rotation_enabled:
		current_map_index = (current_map_index + 1) % available_maps.size()
	
	return map_path

func get_random_map() -> String:
	"""Get a random map"""
	if available_maps.size() == 0:
		return create_default_map()
	
	var random_index = randi() % available_maps.size()
	return available_maps[random_index]

func load_map(map_path: String) -> Node:
	"""Load a specific map"""
	print("Loading Prophaunt map: ", map_path)
	
	# Unload current map
	unload_current_map()
	
	# Load new map
	var map_scene: PackedScene
	
	if ResourceLoader.exists(map_path):
		map_scene = load(map_path)
	else:
		# Create a default map if the specified map doesn't exist
		print("Map not found, creating default map: ", map_path)
		map_scene = create_default_map_scene()
	
	if map_scene:
		current_map = map_scene.instantiate()
		current_map.name = "ProphauntMap"
		
		# Add to scene tree (parent should be set by caller)
		# get_tree().current_scene.add_child(current_map)
		
		# Initialize map
		initialize_loaded_map(map_path)
		
		map_loaded.emit(get_map_name_from_path(map_path))
		return current_map
	
	print("Failed to load map: ", map_path)
	return null

func unload_current_map():
	"""Unload the current map"""
	if current_map:
		var map_name = current_map.name
		current_map.queue_free()
		current_map = null
		map_unloaded.emit(map_name)
		print("Unloaded map: ", map_name)

func initialize_loaded_map(map_path: String):
	"""Initialize a loaded map for Prophaunt gameplay"""
	if not current_map:
		return
	
	var map_name = get_map_name_from_path(map_path)
	
	# Generate spawn points
	generate_prop_spawn_points(map_name)
	generate_haunter_spawn_points(map_name)
	
	# Scan for existing prop objects
	scan_map_for_prop_objects()
	
	# Set up map-specific features
	setup_map_features(map_name)

func generate_prop_spawn_points(map_name: String):
	"""Generate spawn points for props"""
	prop_spawn_points.clear()
	
	var metadata = map_metadata.get(map_name, {})
	var spawn_areas = metadata.get("spawn_areas", {})
	var props_center = spawn_areas.get("props_center", Vector3(0, 1, 10))
	
	# Generate spawn points in a grid around the center
	var spawn_radius = 15.0
	var points_per_ring = 8
	var num_rings = 3
	
	for ring in range(num_rings):
		var ring_radius = spawn_radius * (ring + 1) / num_rings
		var points_in_ring = points_per_ring + ring * 2
		
		for i in range(points_in_ring):
			var angle = (2.0 * PI * i) / points_in_ring
			var x = props_center.x + cos(angle) * ring_radius
			var z = props_center.z + sin(angle) * ring_radius
			var y = props_center.y
			
			# Add some random variation
			x += randf_range(-2.0, 2.0)
			z += randf_range(-2.0, 2.0)
			
			prop_spawn_points.append(Vector3(x, y, z))
	
	prop_spawn_points_generated.emit(prop_spawn_points)
	print("Generated ", prop_spawn_points.size(), " prop spawn points")

func generate_haunter_spawn_points(map_name: String):
	"""Generate spawn points for haunters"""
	haunter_spawn_points.clear()
	
	var metadata = map_metadata.get(map_name, {})
	var spawn_areas = metadata.get("spawn_areas", {})
	var haunters_center = spawn_areas.get("haunters", Vector3(0, 1, -20))
	
	# Generate spawn points in a line or small area
	var spawn_width = 10.0
	var max_haunters = 6  # Reasonable maximum
	
	for i in range(max_haunters):
		var x = haunters_center.x + (i - max_haunters / 2.0) * (spawn_width / max_haunters)
		var y = haunters_center.y
		var z = haunters_center.z + randf_range(-2.0, 2.0)
		
		haunter_spawn_points.append(Vector3(x, y, z))
	
	haunter_spawn_points_generated.emit(haunter_spawn_points)
	print("Generated ", haunter_spawn_points.size(), " haunter spawn points")

func scan_map_for_prop_objects():
	"""Scan the current map for objects that props can mimic"""
	available_prop_objects.clear()
	
	if not current_map:
		return
	
	# This would scan the map for MeshInstance3D nodes that could be mimicked
	# For now, we'll use the recommended props from metadata
	var map_name = get_map_name_from_path("")
	var metadata = map_metadata.get(map_name, {})
	available_prop_objects = metadata.get("recommended_props", ["box", "barrel", "chair"])
	
	print("Found ", available_prop_objects.size(), " prop object types in map")

func setup_map_features(map_name: String):
	"""Set up map-specific features"""
	if not current_map:
		return
	
	# Add map-specific lighting, sounds, or interactive elements
	print("Setting up features for map: ", map_name)

func get_prop_spawn_point(index: int) -> Vector3:
	"""Get a specific prop spawn point"""
	if index < prop_spawn_points.size():
		return prop_spawn_points[index]
	
	# Return a random point if index is out of range
	if prop_spawn_points.size() > 0:
		return prop_spawn_points[randi() % prop_spawn_points.size()]
	
	# Fallback
	return Vector3(randf_range(-10, 10), 1, randf_range(-10, 10))

func get_haunter_spawn_point(index: int) -> Vector3:
	"""Get a specific haunter spawn point"""
	if index < haunter_spawn_points.size():
		return haunter_spawn_points[index]
	
	# Return a random point if index is out of range
	if haunter_spawn_points.size() > 0:
		return haunter_spawn_points[randi() % haunter_spawn_points.size()]
	
	# Fallback
	return Vector3(0, 1, -20)

func get_available_prop_objects() -> Array:
	"""Get list of available prop objects for the current map"""
	return available_prop_objects.duplicate()

func get_random_prop_object() -> String:
	"""Get a random prop object type"""
	if available_prop_objects.size() > 0:
		return available_prop_objects[randi() % available_prop_objects.size()]
	return "box"  # Default fallback

func create_default_map() -> String:
	"""Create a default map path"""
	return "res://prophaunt/maps/default_map.tscn"

func create_default_map_scene() -> PackedScene:
	"""Create a default map scene for testing"""
	var scene = PackedScene.new()
	var root = Node3D.new()
	root.name = "DefaultProphauntMap"
	
	# Add a simple floor
	var floor = StaticBody3D.new()
	var floor_mesh = MeshInstance3D.new()
	var floor_collision = CollisionShape3D.new()
	
	floor_mesh.mesh = BoxMesh.new()
	floor_mesh.mesh.size = Vector3(50, 1, 50)
	floor_mesh.position = Vector3(0, -0.5, 0)
	
	var box_shape = BoxShape3D.new()
	box_shape.size = Vector3(50, 1, 50)
	floor_collision.shape = box_shape
	floor_collision.position = Vector3(0, -0.5, 0)
	
	floor.add_child(floor_mesh)
	floor.add_child(floor_collision)
	root.add_child(floor)
	
	# Add some basic lighting
	var light = DirectionalLight3D.new()
	light.position = Vector3(0, 10, 0)
	light.rotation_degrees = Vector3(-45, 0, 0)
	root.add_child(light)
	
	scene.pack(root)
	return scene

func get_map_name_from_path(path: String) -> String:
	"""Extract map name from file path"""
	var file_name = path.get_file().get_basename()
	return file_name.replace("_map", "").replace("-", " ").capitalize()

# Map rotation management
func set_map_rotation(enabled: bool):
	"""Enable or disable map rotation"""
	map_rotation_enabled = enabled

func reset_map_rotation():
	"""Reset map rotation to the first map"""
	current_map_index = 0

func get_current_map_index() -> int:
	"""Get the current map index"""
	return current_map_index

func get_available_maps() -> Array:
	"""Get list of available maps"""
	return available_maps.duplicate()

func get_map_metadata(map_name: String) -> Dictionary:
	"""Get metadata for a specific map"""
	return map_metadata.get(map_name, {})

# Utility functions
func is_map_loaded() -> bool:
	"""Check if a map is currently loaded"""
	return current_map != null

func get_current_map() -> Node:
	"""Get the current map node"""
	return current_map

func get_map_bounds() -> AABB:
	"""Get the bounds of the current map"""
	if not current_map:
		return AABB(Vector3(-25, 0, -25), Vector3(50, 10, 50))
	
	# Calculate bounds from map geometry
	# For now, return default bounds
	return AABB(Vector3(-25, 0, -25), Vector3(50, 10, 50))
