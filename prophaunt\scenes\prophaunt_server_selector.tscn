[gd_scene load_steps=3 format=3 uid="uid://bh9server1"]

[ext_resource type="Script" path="res://prophaunt/scripts/prophaunt_server_selector.gd" id="1_server_script"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_1"]
bg_color = Color(0.2, 0.2, 0.2, 0.8)
corner_radius_top_left = 10
corner_radius_top_right = 10
corner_radius_bottom_right = 10
corner_radius_bottom_left = 10

[node name="ProphauntServerSelector" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
script = ExtResource("1_server_script")

[node name="Background" type="ColorRect" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
color = Color(0.1, 0.1, 0.2, 1)

[node name="CenterContainer" type="CenterContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0

[node name="MainPanel" type="Panel" parent="CenterContainer"]
layout_mode = 2
custom_minimum_size = Vector2(600, 400)
theme_override_styles/panel = SubResource("StyleBoxFlat_1")

[node name="MainContainer" type="VBoxContainer" parent="CenterContainer/MainPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 20.0
offset_top = 20.0
offset_right = -20.0
offset_bottom = -20.0

[node name="Header" type="VBoxContainer" parent="CenterContainer/MainPanel/MainContainer"]
layout_mode = 2

[node name="TitleLabel" type="Label" parent="CenterContainer/MainPanel/MainContainer/Header"]
layout_mode = 2
text = "PROPHAUNT SERVERS"
horizontal_alignment = 1

[node name="StatusLabel" type="Label" parent="CenterContainer/MainPanel/MainContainer/Header"]
layout_mode = 2
text = "Searching for servers..."
horizontal_alignment = 1

[node name="HSeparator" type="HSeparator" parent="CenterContainer/MainPanel/MainContainer"]
layout_mode = 2

[node name="ServerListContainer" type="VBoxContainer" parent="CenterContainer/MainPanel/MainContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="ServerListLabel" type="Label" parent="CenterContainer/MainPanel/MainContainer/ServerListContainer"]
layout_mode = 2
text = "AVAILABLE SERVERS"

[node name="ServerListPanel" type="Panel" parent="CenterContainer/MainPanel/MainContainer/ServerListContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="ServerListScroll" type="ScrollContainer" parent="CenterContainer/MainPanel/MainContainer/ServerListContainer/ServerListPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_top = 10.0
offset_right = -10.0
offset_bottom = -10.0

[node name="ServerList" type="VBoxContainer" parent="CenterContainer/MainPanel/MainContainer/ServerListContainer/ServerListPanel/ServerListScroll"]
layout_mode = 2
size_flags_horizontal = 3

[node name="HSeparator2" type="HSeparator" parent="CenterContainer/MainPanel/MainContainer"]
layout_mode = 2

[node name="ButtonContainer" type="HBoxContainer" parent="CenterContainer/MainPanel/MainContainer"]
layout_mode = 2

[node name="RefreshButton" type="Button" parent="CenterContainer/MainPanel/MainContainer/ButtonContainer"]
layout_mode = 2
text = "REFRESH"

[node name="CreateServerButton" type="Button" parent="CenterContainer/MainPanel/MainContainer/ButtonContainer"]
layout_mode = 2
text = "CREATE SERVER"

[node name="BackButton" type="Button" parent="CenterContainer/MainPanel/MainContainer/ButtonContainer"]
layout_mode = 2
text = "BACK"

[connection signal="pressed" from="CenterContainer/MainPanel/MainContainer/ButtonContainer/RefreshButton" to="." method="_on_refresh_pressed"]
[connection signal="pressed" from="CenterContainer/MainPanel/MainContainer/ButtonContainer/CreateServerButton" to="." method="_on_create_server_pressed"]
[connection signal="pressed" from="CenterContainer/MainPanel/MainContainer/ButtonContainer/BackButton" to="." method="_on_back_pressed"]
