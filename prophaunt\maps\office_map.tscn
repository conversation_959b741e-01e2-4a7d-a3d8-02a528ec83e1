[gd_scene load_steps=4 format=3 uid="uid://bh8office1"]

[sub_resource type="BoxMesh" id="BoxMesh_1"]
size = Vector3(40, 1, 30)

[sub_resource type="BoxShape3D" id="BoxShape3D_1"]
size = Vector3(40, 1, 30)

[sub_resource type="Environment" id="Environment_1"]
background_mode = 1
background_color = Color(0.9, 0.9, 0.95, 1)
ambient_light_source = 2
ambient_light_color = Color(1, 1, 1, 1)
ambient_light_energy = 0.4

[node name="OfficeMap" type="Node3D"]

[node name="Floor" type="StaticBody3D" parent="."]

[node name="FloorMesh" type="MeshInstance3D" parent="Floor"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, -0.5, 0)
mesh = SubResource("BoxMesh_1")

[node name="FloorCollision" type="CollisionShape3D" parent="Floor"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, -0.5, 0)
shape = SubResource("BoxShape3D_1")

[node name="Walls" type="Node3D" parent="."]

[node name="WallNorth" type="StaticBody3D" parent="Walls"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 2.5, 15)

[node name="WallMesh" type="MeshInstance3D" parent="Walls/WallNorth"]
mesh = SubResource("BoxMesh_1")

[node name="WallCollision" type="CollisionShape3D" parent="Walls/WallNorth"]
shape = SubResource("BoxShape3D_1")

[node name="WallSouth" type="StaticBody3D" parent="Walls"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 2.5, -15)

[node name="WallMesh" type="MeshInstance3D" parent="Walls/WallSouth"]
mesh = SubResource("BoxMesh_1")

[node name="WallCollision" type="CollisionShape3D" parent="Walls/WallSouth"]
shape = SubResource("BoxShape3D_1")

[node name="WallEast" type="StaticBody3D" parent="Walls"]
transform = Transform3D(0, 0, 1, 0, 1, 0, -1, 0, 0, 20, 2.5, 0)

[node name="WallMesh" type="MeshInstance3D" parent="Walls/WallEast"]
mesh = SubResource("BoxMesh_1")

[node name="WallCollision" type="CollisionShape3D" parent="Walls/WallEast"]
shape = SubResource("BoxShape3D_1")

[node name="WallWest" type="StaticBody3D" parent="Walls"]
transform = Transform3D(0, 0, 1, 0, 1, 0, -1, 0, 0, -20, 2.5, 0)

[node name="WallMesh" type="MeshInstance3D" parent="Walls/WallWest"]
mesh = SubResource("BoxMesh_1")

[node name="WallCollision" type="CollisionShape3D" parent="Walls/WallWest"]
shape = SubResource("BoxShape3D_1")

[node name="OfficeFurniture" type="Node3D" parent="."]

[node name="Desk1" type="StaticBody3D" parent="OfficeFurniture"]
transform = Transform3D(2, 0, 0, 0, 0.8, 0, 0, 0, 1, 5, 0.8, 3)

[node name="DeskMesh" type="MeshInstance3D" parent="OfficeFurniture/Desk1"]
mesh = SubResource("BoxMesh_1")

[node name="DeskCollision" type="CollisionShape3D" parent="OfficeFurniture/Desk1"]
shape = SubResource("BoxShape3D_1")

[node name="Chair1" type="StaticBody3D" parent="OfficeFurniture"]
transform = Transform3D(0.6, 0, 0, 0, 1.2, 0, 0, 0, 0.6, 5, 1.2, 5)

[node name="ChairMesh" type="MeshInstance3D" parent="OfficeFurniture/Chair1"]
mesh = SubResource("BoxMesh_1")

[node name="ChairCollision" type="CollisionShape3D" parent="OfficeFurniture/Chair1"]
shape = SubResource("BoxShape3D_1")

[node name="Desk2" type="StaticBody3D" parent="OfficeFurniture"]
transform = Transform3D(2, 0, 0, 0, 0.8, 0, 0, 0, 1, -8, 0.8, -5)

[node name="DeskMesh" type="MeshInstance3D" parent="OfficeFurniture/Desk2"]
mesh = SubResource("BoxMesh_1")

[node name="DeskCollision" type="CollisionShape3D" parent="OfficeFurniture/Desk2"]
shape = SubResource("BoxShape3D_1")

[node name="Chair2" type="StaticBody3D" parent="OfficeFurniture"]
transform = Transform3D(0.6, 0, 0, 0, 1.2, 0, 0, 0, 0.6, -8, 1.2, -3)

[node name="ChairMesh" type="MeshInstance3D" parent="OfficeFurniture/Chair2"]
mesh = SubResource("BoxMesh_1")

[node name="ChairCollision" type="CollisionShape3D" parent="OfficeFurniture/Chair2"]
shape = SubResource("BoxShape3D_1")

[node name="Plant1" type="StaticBody3D" parent="OfficeFurniture"]
transform = Transform3D(0.4, 0, 0, 0, 1.5, 0, 0, 0, 0.4, 12, 1.5, 8)

[node name="PlantMesh" type="MeshInstance3D" parent="OfficeFurniture/Plant1"]
mesh = SubResource("BoxMesh_1")

[node name="PlantCollision" type="CollisionShape3D" parent="OfficeFurniture/Plant1"]
shape = SubResource("BoxShape3D_1")

[node name="TrashCan1" type="StaticBody3D" parent="OfficeFurniture"]
transform = Transform3D(0.5, 0, 0, 0, 1, 0, 0, 0, 0.5, -12, 1, 6)

[node name="TrashCanMesh" type="MeshInstance3D" parent="OfficeFurniture/TrashCan1"]
mesh = SubResource("BoxMesh_1")

[node name="TrashCanCollision" type="CollisionShape3D" parent="OfficeFurniture/TrashCan1"]
shape = SubResource("BoxShape3D_1")

[node name="Computer1" type="StaticBody3D" parent="OfficeFurniture"]
transform = Transform3D(0.8, 0, 0, 0, 0.6, 0, 0, 0, 0.3, 5, 1.4, 3)

[node name="ComputerMesh" type="MeshInstance3D" parent="OfficeFurniture/Computer1"]
mesh = SubResource("BoxMesh_1")

[node name="ComputerCollision" type="CollisionShape3D" parent="OfficeFurniture/Computer1"]
shape = SubResource("BoxShape3D_1")

[node name="Computer2" type="StaticBody3D" parent="OfficeFurniture"]
transform = Transform3D(0.8, 0, 0, 0, 0.6, 0, 0, 0, 0.3, -8, 1.4, -5)

[node name="ComputerMesh" type="MeshInstance3D" parent="OfficeFurniture/Computer2"]
mesh = SubResource("BoxMesh_1")

[node name="ComputerCollision" type="CollisionShape3D" parent="OfficeFurniture/Computer2"]
shape = SubResource("BoxShape3D_1")

[node name="FilingCabinet1" type="StaticBody3D" parent="OfficeFurniture"]
transform = Transform3D(0.8, 0, 0, 0, 1.5, 0, 0, 0, 0.6, 15, 1.5, -8)

[node name="FilingCabinetMesh" type="MeshInstance3D" parent="OfficeFurniture/FilingCabinet1"]
mesh = SubResource("BoxMesh_1")

[node name="FilingCabinetCollision" type="CollisionShape3D" parent="OfficeFurniture/FilingCabinet1"]
shape = SubResource("BoxShape3D_1")

[node name="Lighting" type="Node3D" parent="."]

[node name="MainLight" type="DirectionalLight3D" parent="Lighting"]
transform = Transform3D(0.707107, -0.5, 0.5, 0, 0.707107, 0.707107, -0.707107, -0.5, 0.5, 0, 10, 0)
light_energy = 1.0

[node name="OfficeLight1" type="OmniLight3D" parent="Lighting"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 4, 0)
light_energy = 0.5
omni_range = 15.0

[node name="SpawnAreas" type="Node3D" parent="."]

[node name="HaunterSpawn" type="Node3D" parent="SpawnAreas"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -15, 1, 0)

[node name="PropSpawnCenter" type="Node3D" parent="SpawnAreas"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 8, 1, 0)

[node name="Environment" type="Node3D" parent="."]

[node name="WorldEnvironment" type="WorldEnvironment" parent="Environment"]
environment = SubResource("Environment_1")
