[gd_scene load_steps=4 format=3 uid="uid://bh8house1"]

[sub_resource type="BoxMesh" id="BoxMesh_1"]
size = Vector3(30, 1, 25)

[sub_resource type="BoxShape3D" id="BoxShape3D_1"]
size = Vector3(30, 1, 25)

[sub_resource type="Environment" id="Environment_1"]
background_mode = 1
background_color = Color(0.8, 0.85, 0.9, 1)
ambient_light_source = 2
ambient_light_color = Color(1, 0.95, 0.9, 1)
ambient_light_energy = 0.5

[node name="HouseMap" type="Node3D"]

[node name="Floor" type="StaticBody3D" parent="."]

[node name="FloorMesh" type="MeshInstance3D" parent="Floor"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, -0.5, 0)
mesh = SubResource("BoxMesh_1")

[node name="FloorCollision" type="CollisionShape3D" parent="Floor"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, -0.5, 0)
shape = SubResource("BoxShape3D_1")

[node name="Walls" type="Node3D" parent="."]

[node name="WallNorth" type="StaticBody3D" parent="Walls"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 2.5, 12.5)

[node name="WallMesh" type="MeshInstance3D" parent="Walls/WallNorth"]
mesh = SubResource("BoxMesh_1")

[node name="WallCollision" type="CollisionShape3D" parent="Walls/WallNorth"]
shape = SubResource("BoxShape3D_1")

[node name="WallSouth" type="StaticBody3D" parent="Walls"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 2.5, -12.5)

[node name="WallMesh" type="MeshInstance3D" parent="Walls/WallSouth"]
mesh = SubResource("BoxMesh_1")

[node name="WallCollision" type="CollisionShape3D" parent="Walls/WallSouth"]
shape = SubResource("BoxShape3D_1")

[node name="WallEast" type="StaticBody3D" parent="Walls"]
transform = Transform3D(0, 0, 1, 0, 1, 0, -1, 0, 0, 15, 2.5, 0)

[node name="WallMesh" type="MeshInstance3D" parent="Walls/WallEast"]
mesh = SubResource("BoxMesh_1")

[node name="WallCollision" type="CollisionShape3D" parent="Walls/WallEast"]
shape = SubResource("BoxShape3D_1")

[node name="WallWest" type="StaticBody3D" parent="Walls"]
transform = Transform3D(0, 0, 1, 0, 1, 0, -1, 0, 0, -15, 2.5, 0)

[node name="WallMesh" type="MeshInstance3D" parent="Walls/WallWest"]
mesh = SubResource("BoxMesh_1")

[node name="WallCollision" type="CollisionShape3D" parent="Walls/WallWest"]
shape = SubResource("BoxShape3D_1")

[node name="HouseFurniture" type="Node3D" parent="."]

[node name="Sofa" type="StaticBody3D" parent="HouseFurniture"]
transform = Transform3D(3, 0, 0, 0, 0.8, 0, 0, 0, 1.2, 0, 0.8, 8)

[node name="SofaMesh" type="MeshInstance3D" parent="HouseFurniture/Sofa"]
mesh = SubResource("BoxMesh_1")

[node name="SofaCollision" type="CollisionShape3D" parent="HouseFurniture/Sofa"]
shape = SubResource("BoxShape3D_1")

[node name="CoffeeTable" type="StaticBody3D" parent="HouseFurniture"]
transform = Transform3D(1.5, 0, 0, 0, 0.5, 0, 0, 0, 0.8, 0, 0.5, 5)

[node name="CoffeeTableMesh" type="MeshInstance3D" parent="HouseFurniture/CoffeeTable"]
mesh = SubResource("BoxMesh_1")

[node name="CoffeeTableCollision" type="CollisionShape3D" parent="HouseFurniture/CoffeeTable"]
shape = SubResource("BoxShape3D_1")

[node name="TV" type="StaticBody3D" parent="HouseFurniture"]
transform = Transform3D(2, 0, 0, 0, 1.2, 0, 0, 0, 0.3, 0, 1.2, 10)

[node name="TVMesh" type="MeshInstance3D" parent="HouseFurniture/TV"]
mesh = SubResource("BoxMesh_1")

[node name="TVCollision" type="CollisionShape3D" parent="HouseFurniture/TV"]
shape = SubResource("BoxShape3D_1")

[node name="Armchair1" type="StaticBody3D" parent="HouseFurniture"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -6, 1, 6)

[node name="ArmchairMesh" type="MeshInstance3D" parent="HouseFurniture/Armchair1"]
mesh = SubResource("BoxMesh_1")

[node name="ArmchairCollision" type="CollisionShape3D" parent="HouseFurniture/Armchair1"]
shape = SubResource("BoxShape3D_1")

[node name="Armchair2" type="StaticBody3D" parent="HouseFurniture"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 6, 1, 6)

[node name="ArmchairMesh" type="MeshInstance3D" parent="HouseFurniture/Armchair2"]
mesh = SubResource("BoxMesh_1")

[node name="ArmchairCollision" type="CollisionShape3D" parent="HouseFurniture/Armchair2"]
shape = SubResource("BoxShape3D_1")

[node name="DiningTable" type="StaticBody3D" parent="HouseFurniture"]
transform = Transform3D(2, 0, 0, 0, 0.8, 0, 0, 0, 1.5, -8, 0.8, -5)

[node name="DiningTableMesh" type="MeshInstance3D" parent="HouseFurniture/DiningTable"]
mesh = SubResource("BoxMesh_1")

[node name="DiningTableCollision" type="CollisionShape3D" parent="HouseFurniture/DiningTable"]
shape = SubResource("BoxShape3D_1")

[node name="DiningChair1" type="StaticBody3D" parent="HouseFurniture"]
transform = Transform3D(0.6, 0, 0, 0, 1.2, 0, 0, 0, 0.6, -10, 1.2, -5)

[node name="DiningChairMesh" type="MeshInstance3D" parent="HouseFurniture/DiningChair1"]
mesh = SubResource("BoxMesh_1")

[node name="DiningChairCollision" type="CollisionShape3D" parent="HouseFurniture/DiningChair1"]
shape = SubResource("BoxShape3D_1")

[node name="DiningChair2" type="StaticBody3D" parent="HouseFurniture"]
transform = Transform3D(0.6, 0, 0, 0, 1.2, 0, 0, 0, 0.6, -6, 1.2, -5)

[node name="DiningChairMesh" type="MeshInstance3D" parent="HouseFurniture/DiningChair2"]
mesh = SubResource("BoxMesh_1")

[node name="DiningChairCollision" type="CollisionShape3D" parent="HouseFurniture/DiningChair2"]
shape = SubResource("BoxShape3D_1")

[node name="Lamp1" type="StaticBody3D" parent="HouseFurniture"]
transform = Transform3D(0.3, 0, 0, 0, 1.8, 0, 0, 0, 0.3, 10, 1.8, 3)

[node name="LampMesh" type="MeshInstance3D" parent="HouseFurniture/Lamp1"]
mesh = SubResource("BoxMesh_1")

[node name="LampCollision" type="CollisionShape3D" parent="HouseFurniture/Lamp1"]
shape = SubResource("BoxShape3D_1")

[node name="Bookshelf" type="StaticBody3D" parent="HouseFurniture"]
transform = Transform3D(0.8, 0, 0, 0, 2, 0, 0, 0, 3, 12, 2, -8)

[node name="BookshelfMesh" type="MeshInstance3D" parent="HouseFurniture/Bookshelf"]
mesh = SubResource("BoxMesh_1")

[node name="BookshelfCollision" type="CollisionShape3D" parent="HouseFurniture/Bookshelf"]
shape = SubResource("BoxShape3D_1")

[node name="Plant1" type="StaticBody3D" parent="HouseFurniture"]
transform = Transform3D(0.4, 0, 0, 0, 1.2, 0, 0, 0, 0.4, -10, 1.2, 8)

[node name="PlantMesh" type="MeshInstance3D" parent="HouseFurniture/Plant1"]
mesh = SubResource("BoxMesh_1")

[node name="PlantCollision" type="CollisionShape3D" parent="HouseFurniture/Plant1"]
shape = SubResource("BoxShape3D_1")

[node name="Lighting" type="Node3D" parent="."]

[node name="MainLight" type="DirectionalLight3D" parent="Lighting"]
transform = Transform3D(0.707107, -0.5, 0.5, 0, 0.707107, 0.707107, -0.707107, -0.5, 0.5, 0, 8, 0)
light_energy = 0.8

[node name="LivingRoomLight" type="OmniLight3D" parent="Lighting"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 3, 6)
light_energy = 0.6
omni_range = 12.0

[node name="DiningRoomLight" type="OmniLight3D" parent="Lighting"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, -8, 3, -5)
light_energy = 0.5
omni_range = 10.0

[node name="SpawnAreas" type="Node3D" parent="."]

[node name="HaunterSpawn" type="Node3D" parent="SpawnAreas"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 1, -10)

[node name="PropSpawnCenter" type="Node3D" parent="SpawnAreas"]
transform = Transform3D(1, 0, 0, 0, 1, 0, 0, 0, 1, 0, 1, 5)

[node name="Environment" type="Node3D" parent="."]

[node name="WorldEnvironment" type="WorldEnvironment" parent="Environment"]
environment = SubResource("Environment_1")
