extends Node
class_name ProphauntDisguiseSystem

# Prophaunt disguise system for Props
# Handles visual disguises and prop transformations

var character: Character
var prophaunt_player: ProphauntPlayer

# Disguise data
var current_disguise: String = ""
var available_disguises: Array = []
var disguise_cooldown: float = 5.0
var last_disguise_change: float = 0.0

# Visual components
var original_mesh: MeshInstance3D
var disguise_mesh: MeshInstance3D
var disguise_collision: CollisionShape3D

# Disguise definitions
var disguise_data = {
	"box": {
		"mesh_path": "res://prophaunt/assets/props/box.tres",
		"scale": Vector3(1, 1, 1),
		"collision_shape": "box"
	},
	"barrel": {
		"mesh_path": "res://prophaunt/assets/props/barrel.tres",
		"scale": Vector3(1, 1, 1),
		"collision_shape": "cylinder"
	},
	"chair": {
		"mesh_path": "res://prophaunt/assets/props/chair.tres",
		"scale": Vector3(1, 1, 1),
		"collision_shape": "box"
	},
	"table": {
		"mesh_path": "res://prophaunt/assets/props/table.tres",
		"scale": Vector3(1, 1, 1),
		"collision_shape": "box"
	},
	"plant": {
		"mesh_path": "res://prophaunt/assets/props/plant.tres",
		"scale": Vector3(1, 1, 1),
		"collision_shape": "cylinder"
	},
	"lamp": {
		"mesh_path": "res://prophaunt/assets/props/lamp.tres",
		"scale": Vector3(1, 1, 1),
		"collision_shape": "cylinder"
	},
	"trash_can": {
		"mesh_path": "res://prophaunt/assets/props/trash_can.tres",
		"scale": Vector3(1, 1, 1),
		"collision_shape": "cylinder"
	}
}

signal disguise_applied(disguise_type: String)
signal disguise_removed()
signal disguise_cooldown_started(duration: float)

func _ready():
	setup_available_disguises()

func initialize(player_character: Character, prophaunt_comp: ProphauntPlayer):
	"""Initialize the disguise system"""
	character = player_character
	prophaunt_player = prophaunt_comp
	
	# Store original mesh
	if character.has_node("MeshInstance3D"):
		original_mesh = character.get_node("MeshInstance3D")
	
	# Create disguise nodes
	setup_disguise_nodes()

func setup_available_disguises():
	"""Set up the list of available disguises"""
	available_disguises = disguise_data.keys()

func setup_disguise_nodes():
	"""Set up the disguise mesh and collision nodes"""
	if not character:
		return
	
	# Create disguise mesh node
	disguise_mesh = MeshInstance3D.new()
	disguise_mesh.name = "DisguiseMesh"
	disguise_mesh.visible = false
	character.add_child(disguise_mesh)
	
	# Create disguise collision (if needed)
	# This would replace the character's collision when disguised

func can_change_disguise() -> bool:
	"""Check if the player can change disguise"""
	if not prophaunt_player:
		return false
	
	if prophaunt_player.team != Constants.ProphauntTeam.PROPS:
		return false
	
	if prophaunt_player.player_state != Constants.ProphauntPlayerState.ALIVE:
		return false
	
	var current_time = Time.get_ticks_msec() / 1000.0
	return current_time - last_disguise_change >= disguise_cooldown

func apply_disguise(disguise_type: String) -> bool:
	"""Apply a specific disguise"""
	if not can_change_disguise():
		return false
	
	if disguise_type not in available_disguises:
		print("Invalid disguise type: ", disguise_type)
		return false
	
	last_disguise_change = Time.get_ticks_msec() / 1000.0
	current_disguise = disguise_type
	
	# Apply visual disguise
	apply_visual_disguise(disguise_type)
	
	# Start cooldown
	disguise_cooldown_started.emit(disguise_cooldown)
	
	disguise_applied.emit(disguise_type)
	return true

func remove_disguise():
	"""Remove current disguise"""
	if current_disguise == "":
		return
	
	# Restore original appearance
	restore_original_appearance()
	
	current_disguise = ""
	disguise_removed.emit()

func apply_visual_disguise(disguise_type: String):
	"""Apply the visual aspects of the disguise"""
	var disguise_info = disguise_data.get(disguise_type, {})
	
	if not disguise_mesh:
		return
	
	# Hide original character mesh
	if original_mesh:
		original_mesh.visible = false
	
	# Load and apply disguise mesh
	var mesh_path = disguise_info.get("mesh_path", "")
	if mesh_path != "" and ResourceLoader.exists(mesh_path):
		var mesh_resource = load(mesh_path)
		disguise_mesh.mesh = mesh_resource
	else:
		# Fallback: create a simple box mesh
		var box_mesh = BoxMesh.new()
		box_mesh.size = Vector3(1, 1, 1)
		disguise_mesh.mesh = box_mesh
	
	# Apply scale
	var scale = disguise_info.get("scale", Vector3(1, 1, 1))
	disguise_mesh.scale = scale
	
	# Show disguise mesh
	disguise_mesh.visible = true
	
	print("Applied disguise: ", disguise_type)

func restore_original_appearance():
	"""Restore the character's original appearance"""
	# Hide disguise mesh
	if disguise_mesh:
		disguise_mesh.visible = false
	
	# Show original mesh
	if original_mesh:
		original_mesh.visible = true
	
	print("Restored original appearance")

func get_random_disguise() -> String:
	"""Get a random disguise type"""
	if available_disguises.size() == 0:
		return ""
	
	return available_disguises[randi() % available_disguises.size()]

func get_disguise_cooldown_remaining() -> float:
	"""Get remaining disguise cooldown"""
	var current_time = Time.get_ticks_msec() / 1000.0
	var remaining = disguise_cooldown - (current_time - last_disguise_change)
	return max(0, remaining)

func is_disguised() -> bool:
	"""Check if currently disguised"""
	return current_disguise != ""

func get_current_disguise() -> String:
	"""Get the current disguise type"""
	return current_disguise

# Map-specific disguise functions
func get_map_appropriate_disguises(map_name: String) -> Array:
	"""Get disguises appropriate for a specific map"""
	# This could be expanded to return different disguises based on the map
	match map_name:
		"warehouse":
			return ["box", "barrel", "crate"]
		"office":
			return ["chair", "table", "plant", "trash_can"]
		"house":
			return ["chair", "table", "plant", "lamp"]
		_:
			return available_disguises

func scan_map_for_props() -> Array:
	"""Scan the current map for prop objects that can be mimicked"""
	var found_props = []
	
	# This would scan the map for objects that props could disguise as
	# For now, return default disguises
	return available_disguises

# Hex ability for props
func cast_hex_ability() -> bool:
	"""Cast hex ability to stun nearby haunters"""
	if not prophaunt_player:
		return false
	
	if prophaunt_player.team != Constants.ProphauntTeam.PROPS:
		return false
	
	if prophaunt_player.hex_cooldown > 0:
		return false
	
	# Set cooldown
	prophaunt_player.hex_cooldown = Constants.PROPHAUNT_HEX_COOLDOWN
	
	# Send hex command to server
	if Constants.is_client() and character:
		var player_position = character.global_position
		multiplayer.rpc_id(1, "prophaunt_hex", player_position)
	
	# Create visual effect
	create_hex_effect()
	
	return true

func create_hex_effect():
	"""Create visual effect for hex casting"""
	if not character:
		return
	
	# This would create a visual effect around the character
	print("Hex effect created at: ", character.global_position)
	
	# Play sound
	SoundManager.play_3d_sound.rpc_id(1, character.global_position, 10, SoundManager.SOUND_TYPE.ORDER)

# Movement restrictions while disguised
func should_restrict_movement() -> bool:
	"""Check if movement should be restricted while disguised"""
	# Some disguises might restrict movement to maintain the illusion
	return false  # For now, allow full movement

func get_movement_speed_modifier() -> float:
	"""Get movement speed modifier based on current disguise"""
	if not is_disguised():
		return 1.0
	
	# Different disguises could have different speed modifiers
	match current_disguise:
		"box", "barrel":
			return 0.8  # Slightly slower
		"chair", "table":
			return 0.7  # Slower
		_:
			return 1.0

# Input handling
func handle_disguise_input():
	"""Handle disguise-related input"""
	if not character or not character.is_me():
		return
	
	# Change disguise
	if Input.is_action_just_pressed("change_disguise"):
		var new_disguise = get_random_disguise()
		apply_disguise(new_disguise)
	
	# Cast hex
	if Input.is_action_just_pressed("cast_hex"):
		cast_hex_ability()
	
	# Remove disguise (for testing)
	if Input.is_action_just_pressed("remove_disguise"):
		remove_disguise()

func _input(event):
	"""Handle input events"""
	if Constants.game_mode != Constants.GameMode.Prophaunt:
		return
	
	if not prophaunt_player or prophaunt_player.team != Constants.ProphauntTeam.PROPS:
		return
	
	handle_disguise_input()
