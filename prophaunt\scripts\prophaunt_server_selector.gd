extends Control
class_name ProphauntServerSelector

# Prophaunt server selector
# Allows players to find and join Prophaunt servers

@onready var status_label = $CenterContainer/MainPanel/MainContainer/Header/StatusLabel
@onready var server_list = $CenterContainer/MainPanel/MainContainer/ServerListContainer/ServerListPanel/ServerListScroll/ServerList
@onready var refresh_button = $CenterContainer/MainPanel/MainContainer/ButtonContainer/RefreshButton
@onready var create_server_button = $CenterContainer/MainPanel/MainContainer/ButtonContainer/CreateServerButton
@onready var back_button = $CenterContainer/MainPanel/MainContainer/ButtonContainer/BackButton

# Server data
var available_servers: Array = []
var selected_server: Dictionary = {}

func _ready():
	# Initialize
	status_label.text = "Searching for Prophaunt servers..."
	
	# Start searching for servers
	search_for_servers()

func search_for_servers():
	"""Search for available Prophaunt servers"""
	clear_server_list()
	status_label.text = "Searching for servers..."
	
	# Simulate server search
	await get_tree().create_timer(1.0).timeout
	
	# Add some mock servers for development
	add_mock_servers()
	
	if available_servers.size() > 0:
		status_label.text = "Found " + str(available_servers.size()) + " server(s)"
		populate_server_list()
	else:
		status_label.text = "No servers found"

func add_mock_servers():
	"""Add mock servers for development"""
	available_servers = [
		{
			"name": "Prophaunt Server #1",
			"players": 4,
			"max_players": 8,
			"map": "Warehouse",
			"ping": 45,
			"status": "In Lobby"
		},
		{
			"name": "Prophaunt Server #2",
			"players": 6,
			"max_players": 10,
			"map": "Office",
			"ping": 67,
			"status": "In Game"
		},
		{
			"name": "Prophaunt Server #3",
			"players": 2,
			"max_players": 6,
			"map": "House",
			"ping": 23,
			"status": "In Lobby"
		}
	]

func populate_server_list():
	"""Populate the server list with available servers"""
	clear_server_list()
	
	for i in range(available_servers.size()):
		var server_data = available_servers[i]
		create_server_entry(i, server_data)

func create_server_entry(index: int, server_data: Dictionary):
	"""Create a server list entry"""
	var entry_container = HBoxContainer.new()
	entry_container.custom_minimum_size.y = 40
	entry_container.name = "ServerEntry_" + str(index)
	
	# Server name
	var name_label = Label.new()
	name_label.text = server_data.get("name", "Unknown Server")
	name_label.size_flags_horizontal = Control.SIZE_EXPAND_FILL
	entry_container.add_child(name_label)
	
	# Players
	var players_label = Label.new()
	var players = server_data.get("players", 0)
	var max_players = server_data.get("max_players", 8)
	players_label.text = str(players) + "/" + str(max_players)
	players_label.custom_minimum_size.x = 60
	players_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	entry_container.add_child(players_label)
	
	# Map
	var map_label = Label.new()
	map_label.text = server_data.get("map", "Unknown")
	map_label.custom_minimum_size.x = 80
	map_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	entry_container.add_child(map_label)
	
	# Ping
	var ping_label = Label.new()
	var ping = server_data.get("ping", 0)
	ping_label.text = str(ping) + "ms"
	ping_label.custom_minimum_size.x = 60
	ping_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	
	# Color code ping
	if ping < 50:
		ping_label.modulate = Color.GREEN
	elif ping < 100:
		ping_label.modulate = Color.YELLOW
	else:
		ping_label.modulate = Color.RED
	
	entry_container.add_child(ping_label)
	
	# Status
	var status_label = Label.new()
	var status = server_data.get("status", "Unknown")
	status_label.text = status
	status_label.custom_minimum_size.x = 80
	status_label.horizontal_alignment = HORIZONTAL_ALIGNMENT_CENTER
	
	# Color code status
	if status == "In Lobby":
		status_label.modulate = Color.GREEN
	elif status == "In Game":
		status_label.modulate = Color.YELLOW
	else:
		status_label.modulate = Color.GRAY
	
	entry_container.add_child(status_label)
	
	# Join button
	var join_button = Button.new()
	join_button.text = "JOIN"
	join_button.custom_minimum_size.x = 60
	
	# Disable if server is full
	if players >= max_players:
		join_button.disabled = true
		join_button.text = "FULL"
	
	join_button.pressed.connect(_on_join_server_pressed.bind(index))
	entry_container.add_child(join_button)
	
	server_list.add_child(entry_container)

func clear_server_list():
	"""Clear the server list"""
	for child in server_list.get_children():
		child.queue_free()

func _on_join_server_pressed(server_index: int):
	"""Handle join server button press"""
	if server_index < available_servers.size():
		var server_data = available_servers[server_index]
		join_server(server_data)

func join_server(server_data: Dictionary):
	"""Join a specific server"""
	print("Joining server: ", server_data.get("name", "Unknown"))
	
	# Set game mode
	Constants.game_mode = Constants.GameMode.Prophaunt
	
	# Store server data
	selected_server = server_data
	
	# Transition to Prophaunt game
	get_tree().change_scene_to_file("res://prophaunt/scenes/prophaunt_game.tscn")

func _on_refresh_pressed():
	"""Handle refresh button press"""
	SoundManager.play_click_sound()
	search_for_servers()

func _on_create_server_pressed():
	"""Handle create server button press"""
	SoundManager.play_click_sound()
	create_prophaunt_server()

func _on_back_pressed():
	"""Handle back button press"""
	SoundManager.play_click_sound()
	get_tree().change_scene_to_file("res://Scenes/main_menu.tscn")

func create_prophaunt_server():
	"""Create a new Prophaunt server"""
	print("Creating Prophaunt server...")
	
	# Set game mode
	Constants.game_mode = Constants.GameMode.Prophaunt
	
	# Set server mode
	Constants.is_server = true
	
	# Transition to Prophaunt game as server
	get_tree().change_scene_to_file("res://prophaunt/scenes/prophaunt_game.tscn")

func _process(_delta):
	"""Update server selector"""
	if Input.is_action_just_pressed("exit"):
		_on_back_pressed()
