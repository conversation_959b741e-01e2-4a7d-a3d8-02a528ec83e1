extends Node
class_name ProphauntScoringSystem

# Prophaunt scoring and statistics system
# Handles player scoring, statistics tracking, and leaderboard generation

# Scoring constants
const PROP_SURVIVAL_BONUS = 100
const PROP_PARTICIPATION_SCORE = 25
const HAUNTER_WIN_BONUS = 75
const HAUNTER_PARTICIPATION_SCORE = 25
const ELIMINATION_BONUS = 50
const DAMAGE_DEALT_MULTIPLIER = 0.5
const HEX_CAST_BONUS = 10
const DISGUISE_CHANGE_BONUS = 5
const TIME_SURVIVAL_MULTIPLIER = 1.0  # Points per second survived

# Player statistics tracking
var player_stats: Dictionary = {}
var round_stats: Dictionary = {}
var game_stats: Dictionary = {}

signal stats_updated(player_id: int, stats: Dictionary)
signal leaderboard_updated(leaderboard: Array)

func _ready():
	initialize_scoring_system()

func initialize_scoring_system():
	"""Initialize the scoring system"""
	reset_game_stats()
	print("ProphauntScoringSystem initialized")

func reset_game_stats():
	"""Reset all game statistics"""
	player_stats.clear()
	round_stats.clear()
	game_stats = {
		"total_rounds": 0,
		"props_wins": 0,
		"haunters_wins": 0,
		"total_eliminations": 0,
		"total_damage_dealt": 0,
		"total_shots_fired": 0,
		"total_grenades_thrown": 0,
		"total_hexes_cast": 0,
		"total_disguise_changes": 0
	}

func initialize_player(player_id: int, player_name: String, team: Constants.ProphauntTeam):
	"""Initialize a player's statistics"""
	player_stats[player_id] = {
		"player_id": player_id,
		"name": player_name,
		"team": team,
		"total_score": 0,
		"rounds_played": 0,
		"rounds_won": 0,
		"rounds_survived": 0,
		"total_damage_dealt": 0,
		"total_damage_taken": 0,
		"eliminations": 0,
		"deaths": 0,
		"shots_fired": 0,
		"shots_hit": 0,
		"grenades_thrown": 0,
		"hexes_cast": 0,
		"disguise_changes": 0,
		"time_survived": 0.0,
		"best_survival_time": 0.0
	}
	
	print("Initialized stats for player: ", player_name, " (", player_id, ")")

func start_round(round_number: int, players: Dictionary):
	"""Start tracking for a new round"""
	round_stats = {
		"round_number": round_number,
		"start_time": Time.get_ticks_msec() / 1000.0,
		"players": {},
		"winner": -1,
		"win_reason": "",
		"duration": 0.0,
		"eliminations": 0,
		"damage_dealt": 0,
		"shots_fired": 0,
		"grenades_thrown": 0,
		"hexes_cast": 0,
		"disguise_changes": 0
	}
	
	# Initialize round stats for each player
	for player_id in players.keys():
		round_stats["players"][player_id] = {
			"player_id": player_id,
			"team": players[player_id].get("prophaunt_team", -1),
			"survived": true,
			"damage_dealt": 0,
			"damage_taken": 0,
			"shots_fired": 0,
			"shots_hit": 0,
			"grenades_thrown": 0,
			"hexes_cast": 0,
			"disguise_changes": 0,
			"survival_time": 0.0,
			"score": 0
		}
	
	print("Started round ", round_number, " stats tracking")

func end_round(winner_team: Constants.ProphauntTeam, win_reason: String, duration: float):
	"""End round and calculate scores"""
	round_stats["winner"] = winner_team
	round_stats["win_reason"] = win_reason
	round_stats["duration"] = duration
	round_stats["end_time"] = Time.get_ticks_msec() / 1000.0
	
	# Calculate scores for each player
	for player_id in round_stats["players"].keys():
		calculate_round_score(player_id, winner_team, duration)
	
	# Update game statistics
	update_game_stats()
	
	game_stats["total_rounds"] += 1
	if winner_team == Constants.ProphauntTeam.PROPS:
		game_stats["props_wins"] += 1
	elif winner_team == Constants.ProphauntTeam.HAUNTERS:
		game_stats["haunters_wins"] += 1
	
	print("Round ended - Winner: ", "Props" if winner_team == Constants.ProphauntTeam.PROPS else "Haunters")
	return round_stats.duplicate()

func calculate_round_score(player_id: int, winner_team: Constants.ProphauntTeam, round_duration: float):
	"""Calculate score for a player in the round"""
	if player_id not in round_stats["players"]:
		return
	
	var player_round_stats = round_stats["players"][player_id]
	var player_team = player_round_stats["team"]
	var score = 0
	
	# Base participation score
	if player_team == Constants.ProphauntTeam.PROPS:
		score += PROP_PARTICIPATION_SCORE
		
		# Survival bonus
		if player_round_stats["survived"]:
			score += PROP_SURVIVAL_BONUS
			
			# Time survival bonus
			var survival_time = player_round_stats["survival_time"]
			score += int(survival_time * TIME_SURVIVAL_MULTIPLIER)
		
		# Hex casting bonus
		score += player_round_stats["hexes_cast"] * HEX_CAST_BONUS
		
		# Disguise change bonus
		score += player_round_stats["disguise_changes"] * DISGUISE_CHANGE_BONUS
		
	else:  # Haunters
		score += HAUNTER_PARTICIPATION_SCORE
		
		# Win bonus
		if winner_team == Constants.ProphauntTeam.HAUNTERS:
			score += HAUNTER_WIN_BONUS
		
		# Elimination bonus
		score += player_round_stats["shots_hit"] * ELIMINATION_BONUS
		
		# Damage dealt bonus
		score += int(player_round_stats["damage_dealt"] * DAMAGE_DEALT_MULTIPLIER)
	
	# Store round score
	player_round_stats["score"] = score
	
	# Update player total stats
	if player_id in player_stats:
		player_stats[player_id]["total_score"] += score
		player_stats[player_id]["rounds_played"] += 1
		
		if (player_team == winner_team):
			player_stats[player_id]["rounds_won"] += 1
		
		if player_team == Constants.ProphauntTeam.PROPS and player_round_stats["survived"]:
			player_stats[player_id]["rounds_survived"] += 1
		
		# Update other stats
		update_player_total_stats(player_id, player_round_stats)
	
	stats_updated.emit(player_id, player_stats[player_id])

func update_player_total_stats(player_id: int, round_data: Dictionary):
	"""Update player's total statistics"""
	var total_stats = player_stats[player_id]
	
	total_stats["total_damage_dealt"] += round_data.get("damage_dealt", 0)
	total_stats["total_damage_taken"] += round_data.get("damage_taken", 0)
	total_stats["shots_fired"] += round_data.get("shots_fired", 0)
	total_stats["shots_hit"] += round_data.get("shots_hit", 0)
	total_stats["grenades_thrown"] += round_data.get("grenades_thrown", 0)
	total_stats["hexes_cast"] += round_data.get("hexes_cast", 0)
	total_stats["disguise_changes"] += round_data.get("disguise_changes", 0)
	
	var survival_time = round_data.get("survival_time", 0.0)
	total_stats["time_survived"] += survival_time
	
	if survival_time > total_stats["best_survival_time"]:
		total_stats["best_survival_time"] = survival_time
	
	if not round_data.get("survived", true):
		total_stats["deaths"] += 1

func update_game_stats():
	"""Update overall game statistics"""
	game_stats["total_eliminations"] += round_stats.get("eliminations", 0)
	game_stats["total_damage_dealt"] += round_stats.get("damage_dealt", 0)
	game_stats["total_shots_fired"] += round_stats.get("shots_fired", 0)
	game_stats["total_grenades_thrown"] += round_stats.get("grenades_thrown", 0)
	game_stats["total_hexes_cast"] += round_stats.get("hexes_cast", 0)
	game_stats["total_disguise_changes"] += round_stats.get("disguise_changes", 0)

# Event recording functions
func record_shot_fired(player_id: int):
	"""Record a shot being fired"""
	if player_id in round_stats["players"]:
		round_stats["players"][player_id]["shots_fired"] += 1
		round_stats["shots_fired"] += 1

func record_shot_hit(player_id: int, damage: int):
	"""Record a successful hit"""
	if player_id in round_stats["players"]:
		round_stats["players"][player_id]["shots_hit"] += 1
		round_stats["players"][player_id]["damage_dealt"] += damage
		round_stats["damage_dealt"] += damage

func record_grenade_thrown(player_id: int):
	"""Record a grenade being thrown"""
	if player_id in round_stats["players"]:
		round_stats["players"][player_id]["grenades_thrown"] += 1
		round_stats["grenades_thrown"] += 1

func record_hex_cast(player_id: int):
	"""Record a hex being cast"""
	if player_id in round_stats["players"]:
		round_stats["players"][player_id]["hexes_cast"] += 1
		round_stats["hexes_cast"] += 1

func record_disguise_change(player_id: int):
	"""Record a disguise change"""
	if player_id in round_stats["players"]:
		round_stats["players"][player_id]["disguise_changes"] += 1
		round_stats["disguise_changes"] += 1

func record_damage_taken(player_id: int, damage: int):
	"""Record damage taken by a player"""
	if player_id in round_stats["players"]:
		round_stats["players"][player_id]["damage_taken"] += damage

func record_player_elimination(player_id: int):
	"""Record a player elimination"""
	if player_id in round_stats["players"]:
		round_stats["players"][player_id]["survived"] = false
		round_stats["eliminations"] += 1

func update_survival_time(player_id: int, time: float):
	"""Update a player's survival time"""
	if player_id in round_stats["players"]:
		round_stats["players"][player_id]["survival_time"] = time

# Leaderboard generation
func generate_round_leaderboard() -> Array:
	"""Generate leaderboard for the current round"""
	var leaderboard = []
	
	for player_id in round_stats["players"].keys():
		var player_data = round_stats["players"][player_id].duplicate()
		if player_id in player_stats:
			player_data["name"] = player_stats[player_id]["name"]
		leaderboard.append(player_data)
	
	# Sort by score
	leaderboard.sort_custom(func(a, b): return a.get("score", 0) > b.get("score", 0))
	
	return leaderboard

func generate_game_leaderboard() -> Array:
	"""Generate overall game leaderboard"""
	var leaderboard = []
	
	for player_id in player_stats.keys():
		leaderboard.append(player_stats[player_id].duplicate())
	
	# Sort by total score
	leaderboard.sort_custom(func(a, b): return a.get("total_score", 0) > b.get("total_score", 0))
	
	leaderboard_updated.emit(leaderboard)
	return leaderboard

# Getters
func get_player_stats(player_id: int) -> Dictionary:
	"""Get statistics for a specific player"""
	return player_stats.get(player_id, {})

func get_round_stats() -> Dictionary:
	"""Get current round statistics"""
	return round_stats.duplicate()

func get_game_stats() -> Dictionary:
	"""Get overall game statistics"""
	return game_stats.duplicate()

func get_team_stats(team: Constants.ProphauntTeam) -> Dictionary:
	"""Get statistics for a specific team"""
	var team_stats = {
		"total_players": 0,
		"total_score": 0,
		"average_score": 0.0,
		"rounds_won": 0,
		"survival_rate": 0.0
	}
	
	var team_players = []
	for player_id in player_stats.keys():
		if player_stats[player_id]["team"] == team:
			team_players.append(player_stats[player_id])
	
	team_stats["total_players"] = team_players.size()
	
	if team_players.size() > 0:
		var total_score = 0
		var total_wins = 0
		var total_survivals = 0
		
		for player in team_players:
			total_score += player.get("total_score", 0)
			total_wins += player.get("rounds_won", 0)
			total_survivals += player.get("rounds_survived", 0)
		
		team_stats["total_score"] = total_score
		team_stats["average_score"] = float(total_score) / float(team_players.size())
		team_stats["rounds_won"] = total_wins
		
		if team == Constants.ProphauntTeam.PROPS:
			var total_rounds_played = team_players[0].get("rounds_played", 1) * team_players.size()
			team_stats["survival_rate"] = float(total_survivals) / float(total_rounds_played)
	
	return team_stats

# Utility functions
func format_accuracy(shots_fired: int, shots_hit: int) -> String:
	"""Format accuracy as percentage"""
	if shots_fired == 0:
		return "0%"
	return str(int(float(shots_hit) / float(shots_fired) * 100)) + "%"

func format_survival_rate(survived: int, total: int) -> String:
	"""Format survival rate as percentage"""
	if total == 0:
		return "0%"
	return str(int(float(survived) / float(total) * 100)) + "%"
