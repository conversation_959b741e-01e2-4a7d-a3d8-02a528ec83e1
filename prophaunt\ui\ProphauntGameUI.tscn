[gd_scene load_steps=3 format=3 uid="uid://bh9xvn9ywqp8ys"]

[ext_resource type="Script" path="res://prophaunt/scripts/ProphauntGameUI.gd" id="1_ui_script"]

[sub_resource type="StyleBoxFlat" id="StyleBoxFlat_1"]
bg_color = Color(0, 0, 0, 0.7)
corner_radius_top_left = 5
corner_radius_top_right = 5
corner_radius_bottom_right = 5
corner_radius_bottom_left = 5

[node name="ProphauntGameUI" type="Control"]
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
mouse_filter = 2
script = ExtResource("1_ui_script")

[node name="TopPanel" type="Panel" parent="."]
layout_mode = 1
anchors_preset = 2
anchor_top = 0.0
anchor_bottom = 0.0
offset_right = 400.0
offset_bottom = 80.0
theme_override_styles/panel = SubResource("StyleBoxFlat_1")

[node name="TopContainer" type="HBoxContainer" parent="TopPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_top = 10.0
offset_right = -10.0
offset_bottom = -10.0

[node name="RoundInfo" type="VBoxContainer" parent="TopPanel/TopContainer"]
layout_mode = 2

[node name="RoundLabel" type="Label" parent="TopPanel/TopContainer/RoundInfo"]
layout_mode = 2
text = "ROUND 1"
horizontal_alignment = 1

[node name="TimerLabel" type="Label" parent="TopPanel/TopContainer/RoundInfo"]
layout_mode = 2
text = "03:00"
horizontal_alignment = 1

[node name="VSeparator" type="VSeparator" parent="TopPanel/TopContainer"]
layout_mode = 2

[node name="TeamInfo" type="VBoxContainer" parent="TopPanel/TopContainer"]
layout_mode = 2

[node name="TeamLabel" type="Label" parent="TopPanel/TopContainer/TeamInfo"]
layout_mode = 2
text = "PROPS"
horizontal_alignment = 1

[node name="PropsAliveLabel" type="Label" parent="TopPanel/TopContainer/TeamInfo"]
layout_mode = 2
text = "5/6 Alive"
horizontal_alignment = 1

[node name="HealthPanel" type="Panel" parent="."]
layout_mode = 1
anchors_preset = 3
anchor_left = 1.0
anchor_top = 0.0
anchor_right = 1.0
anchor_bottom = 0.0
offset_left = -200.0
offset_bottom = 60.0
theme_override_styles/panel = SubResource("StyleBoxFlat_1")

[node name="HealthContainer" type="VBoxContainer" parent="HealthPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_top = 10.0
offset_right = -10.0
offset_bottom = -10.0

[node name="HealthLabel" type="Label" parent="HealthPanel/HealthContainer"]
layout_mode = 2
text = "HEALTH"
horizontal_alignment = 1

[node name="HealthBar" type="ProgressBar" parent="HealthPanel/HealthContainer"]
layout_mode = 2
max_value = 100.0
value = 100.0
show_percentage = false

[node name="AbilitiesPanel" type="Panel" parent="."]
layout_mode = 1
anchors_preset = 7
anchor_left = 0.5
anchor_top = 1.0
anchor_right = 0.5
anchor_bottom = 1.0
offset_left = -150.0
offset_top = -100.0
offset_right = 150.0
theme_override_styles/panel = SubResource("StyleBoxFlat_1")

[node name="AbilitiesContainer" type="HBoxContainer" parent="AbilitiesPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_top = 10.0
offset_right = -10.0
offset_bottom = -10.0

[node name="Ability1" type="VBoxContainer" parent="AbilitiesPanel/AbilitiesContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="Ability1Button" type="Button" parent="AbilitiesPanel/AbilitiesContainer/Ability1"]
layout_mode = 2
text = "SHOOT"

[node name="Ability1Cooldown" type="Label" parent="AbilitiesPanel/AbilitiesContainer/Ability1"]
layout_mode = 2
text = ""
horizontal_alignment = 1

[node name="Ability2" type="VBoxContainer" parent="AbilitiesPanel/AbilitiesContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="Ability2Button" type="Button" parent="AbilitiesPanel/AbilitiesContainer/Ability2"]
layout_mode = 2
text = "GRENADE"

[node name="Ability2Cooldown" type="Label" parent="AbilitiesPanel/AbilitiesContainer/Ability2"]
layout_mode = 2
text = ""
horizontal_alignment = 1

[node name="Ability3" type="VBoxContainer" parent="AbilitiesPanel/AbilitiesContainer"]
layout_mode = 2
size_flags_horizontal = 3

[node name="Ability3Button" type="Button" parent="AbilitiesPanel/AbilitiesContainer/Ability3"]
layout_mode = 2
text = "HEX"

[node name="Ability3Cooldown" type="Label" parent="AbilitiesPanel/AbilitiesContainer/Ability3"]
layout_mode = 2
text = ""
horizontal_alignment = 1

[node name="ChatPanel" type="Panel" parent="."]
layout_mode = 1
anchors_preset = 6
anchor_left = 0.0
anchor_top = 0.5
anchor_right = 0.0
anchor_bottom = 1.0
offset_right = 300.0
offset_top = 50.0
offset_bottom = -10.0
theme_override_styles/panel = SubResource("StyleBoxFlat_1")

[node name="ChatContainer" type="VBoxContainer" parent="ChatPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_top = 10.0
offset_right = -10.0
offset_bottom = -10.0

[node name="ChatLabel" type="Label" parent="ChatPanel/ChatContainer"]
layout_mode = 2
text = "TEAM CHAT"

[node name="ChatScroll" type="ScrollContainer" parent="ChatPanel/ChatContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="ChatMessages" type="VBoxContainer" parent="ChatPanel/ChatContainer/ChatScroll"]
layout_mode = 2
size_flags_horizontal = 3

[node name="ChatInput" type="LineEdit" parent="ChatPanel/ChatContainer"]
layout_mode = 2
placeholder_text = "Type message..."

[node name="PlayerListPanel" type="Panel" parent="."]
layout_mode = 1
anchors_preset = 1
anchor_left = 1.0
anchor_right = 1.0
offset_left = -250.0
offset_top = 100.0
offset_bottom = 400.0
theme_override_styles/panel = SubResource("StyleBoxFlat_1")

[node name="PlayerListContainer" type="VBoxContainer" parent="PlayerListPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 10.0
offset_top = 10.0
offset_right = -10.0
offset_bottom = -10.0

[node name="PlayerListLabel" type="Label" parent="PlayerListPanel/PlayerListContainer"]
layout_mode = 2
text = "PLAYERS"
horizontal_alignment = 1

[node name="HSeparator" type="HSeparator" parent="PlayerListPanel/PlayerListContainer"]
layout_mode = 2

[node name="PropsLabel" type="Label" parent="PlayerListPanel/PlayerListContainer"]
layout_mode = 2
text = "PROPS"
horizontal_alignment = 1

[node name="PropsList" type="VBoxContainer" parent="PlayerListPanel/PlayerListContainer"]
layout_mode = 2

[node name="HSeparator2" type="HSeparator" parent="PlayerListPanel/PlayerListContainer"]
layout_mode = 2

[node name="HauntersLabel" type="Label" parent="PlayerListPanel/PlayerListContainer"]
layout_mode = 2
text = "HAUNTERS"
horizontal_alignment = 1

[node name="HauntersList" type="VBoxContainer" parent="PlayerListPanel/PlayerListContainer"]
layout_mode = 2

[node name="NotificationPanel" type="Panel" parent="."]
layout_mode = 1
anchors_preset = 8
anchor_left = 0.5
anchor_top = 0.5
anchor_right = 0.5
anchor_bottom = 0.5
offset_left = -200.0
offset_top = -50.0
offset_right = 200.0
offset_bottom = 50.0
visible = false
theme_override_styles/panel = SubResource("StyleBoxFlat_1")

[node name="NotificationLabel" type="Label" parent="NotificationPanel"]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
text = "NOTIFICATION"
horizontal_alignment = 1
vertical_alignment = 1

[connection signal="pressed" from="AbilitiesPanel/AbilitiesContainer/Ability1/Ability1Button" to="." method="_on_ability_1_pressed"]
[connection signal="pressed" from="AbilitiesPanel/AbilitiesContainer/Ability2/Ability2Button" to="." method="_on_ability_2_pressed"]
[connection signal="pressed" from="AbilitiesPanel/AbilitiesContainer/Ability3/Ability3Button" to="." method="_on_ability_3_pressed"]
[connection signal="text_submitted" from="ChatPanel/ChatContainer/ChatInput" to="." method="_on_chat_submitted"]
