extends Control
class_name ProphauntGameController

# Prophaunt client-side game controller
# Manages the client-side game state and UI

@onready var status_label = $CenterContainer/VBoxContainer/Status
@onready var back_button = $CenterContainer/VBoxContainer/BackButton

# Game UI
var game_ui: ProphauntGameUI
var game_ui_scene = preload("res://prophaunt/ui/ProphauntGameUI.tscn")

# Game state
var local_player_id: int = -1
var current_team: Constants.ProphauntTeam = -1
var game_started: bool = false
var connected_to_server: bool = false

# Player management
var prophaunt_player_manager: ProphauntPlayerManager

func _ready():
	# Initialize
	local_player_id = multiplayer.get_unique_id()
	status_label.text = "Connecting to Prophaunt server..."
	back_button.text = tr("BACK_TO_MAIN_MENU")

	# Initialize player manager
	prophaunt_player_manager = ProphauntPlayerManager.get_instance()

	# Connect to server
	connect_to_prophaunt_server()

func connect_to_prophaunt_server():
	"""Connect to a Prophaunt server"""
	# This would typically connect to a server
	# For now, simulate connection
	await get_tree().create_timer(1.0).timeout

	connected_to_server = true
	status_label.text = "Connected! Waiting for game to start..."

	# Initialize game UI
	initialize_game_ui()

func initialize_game_ui():
	"""Initialize the game UI"""
	if game_ui:
		return

	game_ui = game_ui_scene.instantiate()
	add_child(game_ui)

	# Connect UI signals
	game_ui.ability_used.connect(_on_ability_used)
	game_ui.chat_message_sent.connect(_on_chat_message_sent)

	# Hide initial UI elements
	$CenterContainer.visible = false
	game_ui.visible = true

func start_prophaunt_game():
	"""Start the Prophaunt game"""
	game_started = true
	status_label.text = "Game started!"

	if game_ui:
		game_ui.show_notification("PROPHAUNT GAME STARTED!", 3.0)

func set_player_team(team: Constants.ProphauntTeam):
	"""Set the local player's team"""
	current_team = team

	if game_ui:
		game_ui.set_team(team)

	print("Local player assigned to team: ", "Props" if team == Constants.ProphauntTeam.PROPS else "Haunters")

# RPC handlers for server communication
@rpc("authority", "call_local", "reliable")
func prophaunt_lobby_update(lobby_data: Dictionary):
	"""Handle lobby update from server"""
	var players_count = lobby_data.get("j", 0)
	var min_players = lobby_data.get("s", 2)
	var timer = lobby_data.get("t", 0)

	status_label.text = "Lobby: " + str(players_count) + "/" + str(min_players) + " players (" + str(timer) + "s)"

@rpc("authority", "call_local", "reliable")
func prophaunt_round_start(round_number: int, duration: float):
	"""Handle round start"""
	if game_ui:
		game_ui.on_round_started(round_number, duration)

	print("Round ", round_number, " started with duration: ", duration)

@rpc("authority", "call_local", "reliable")
func prophaunt_round_end(winner_team: Constants.ProphauntTeam, results: Dictionary):
	"""Handle round end"""
	if game_ui:
		game_ui.on_round_ended(winner_team)

	print("Round ended - Winner: ", "Props" if winner_team == Constants.ProphauntTeam.PROPS else "Haunters")

@rpc("authority", "call_local", "reliable")
func prophaunt_game_update(game_data: Dictionary):
	"""Handle game state update"""
	var round_timer = game_data.get("round_timer", 0.0)
	var props_alive = game_data.get("props_alive", 0)
	var total_props = game_data.get("total_props", 0)
	var current_round = game_data.get("current_round", 1)

	if game_ui:
		game_ui.update_round_info(current_round, round_timer)
		game_ui.update_props_info(props_alive, total_props)

	# Update local player health if prop
	var player_states = game_data.get("player_states", {})
	if local_player_id in player_states:
		var player_state = player_states[local_player_id]
		var hp = player_state.get("hp", 100)
		var max_hp = 100  # Could be dynamic

		if game_ui and current_team == Constants.ProphauntTeam.PROPS:
			game_ui.update_health(hp, max_hp)

@rpc("authority", "call_local", "reliable")
func prophaunt_player_eliminated(player_id: int):
	"""Handle player elimination"""
	if game_ui:
		game_ui.on_player_eliminated(player_id)

	print("Player ", player_id, " eliminated")

@rpc("authority", "call_local", "reliable")
func prophaunt_player_damaged(player_id: int, new_hp: int):
	"""Handle player damage"""
	if player_id == local_player_id and game_ui:
		game_ui.update_health(new_hp, 100)

	print("Player ", player_id, " damaged - HP: ", new_hp)

@rpc("authority", "call_local", "reliable")
func prophaunt_player_hexed(player_id: int, duration: float):
	"""Handle player hex"""
	if game_ui:
		var message = "Player hexed for " + str(duration) + " seconds!"
		game_ui.show_notification(message, 2.0)

	print("Player ", player_id, " hexed for ", duration, " seconds")

@rpc("authority", "call_local", "reliable")
func prophaunt_player_unhexed(player_id: int):
	"""Handle player unhex"""
	print("Player ", player_id, " unhexed")

@rpc("authority", "call_local", "reliable")
func prophaunt_player_disguised(player_id: int, disguise: String):
	"""Handle player disguise change"""
	print("Player ", player_id, " disguised as: ", disguise)

@rpc("authority", "call_local", "reliable")
func prophaunt_round_warning(warning_type: String, time_remaining: float):
	"""Handle round time warning"""
	if game_ui:
		game_ui.on_time_warning(warning_type)

	print("Round warning: ", warning_type, " - ", time_remaining, " seconds")

@rpc("authority", "call_local", "reliable")
func prophaunt_final_results(results: Dictionary):
	"""Handle final game results"""
	print("Final results: ", results)

	if game_ui:
		game_ui.show_notification("GAME ENDED - CHECK RESULTS!", 5.0)

@rpc("authority", "call_local", "reliable")
func prophaunt_game_ended():
	"""Handle game end"""
	print("Prophaunt game ended")

	if game_ui:
		game_ui.show_notification("GAME ENDED", 3.0)

# UI event handlers
func _on_ability_used(ability_index: int):
	"""Handle ability use from UI"""
	var prophaunt_player = prophaunt_player_manager.get_prophaunt_player(local_player_id)
	if not prophaunt_player:
		return

	match ability_index:
		0:  # First ability
			if current_team == Constants.ProphauntTeam.PROPS:
				# Disguise change
				var new_disguise = prophaunt_player.disguise_system.get_random_disguise()
				prophaunt_player.change_disguise(new_disguise)
			else:
				# Shoot
				var target_pos = get_aim_target()
				prophaunt_player.shoot_at_target(target_pos)
		1:  # Second ability
			if current_team == Constants.ProphauntTeam.PROPS:
				# Hex
				prophaunt_player.cast_hex()
			else:
				# Grenade
				var target_pos = get_aim_target()
				prophaunt_player.throw_grenade(target_pos)
		2:  # Third ability
			# Additional abilities can be added here
			pass

func _on_chat_message_sent(message: String):
	"""Handle chat message from UI"""
	# Send chat message to server
	print("Chat message: ", message)

func get_aim_target() -> Vector3:
	"""Get the current aim target position"""
	# This would typically use the camera and mouse position
	# For now, return a position in front of the player
	return Vector3(0, 0, -5)

func _on_back_button_pressed():
	"""Handle back button press"""
	SoundManager.play_click_sound()

	# Disconnect from server if connected
	if connected_to_server:
		# Disconnect logic here
		pass

	get_tree().change_scene_to_file("res://Scenes/main_menu.tscn")

func _process(_delta):
	"""Update game controller"""
	if Input.is_action_just_pressed("exit"):
		_on_back_button_pressed()

	# Update UI cooldowns if game is active
	if game_started and game_ui and prophaunt_player_manager:
		update_ability_cooldowns()

func update_ability_cooldowns():
	"""Update ability cooldown displays"""
	var prophaunt_player = prophaunt_player_manager.get_prophaunt_player(local_player_id)
	if not prophaunt_player:
		return

	if current_team == Constants.ProphauntTeam.PROPS:
		# Prop abilities
		var disguise_cooldown = 0.0
		var hex_cooldown = prophaunt_player.hex_cooldown

		if prophaunt_player.disguise_system:
			disguise_cooldown = prophaunt_player.disguise_system.get_disguise_cooldown_remaining()

		game_ui.update_ability_cooldown(0, disguise_cooldown)
		game_ui.update_ability_cooldown(1, hex_cooldown)
		game_ui.update_ability_cooldown(2, 0.0)  # No third ability yet
	else:
		# Haunter abilities
		var gun_cooldown = 0.0
		var grenade_cooldown = 0.0

		if prophaunt_player.weapon_system:
			gun_cooldown = prophaunt_player.weapon_system.get_gun_cooldown_remaining()
			grenade_cooldown = prophaunt_player.weapon_system.get_grenade_cooldown_remaining()

		game_ui.update_ability_cooldown(0, gun_cooldown)
		game_ui.update_ability_cooldown(1, grenade_cooldown)
		game_ui.update_ability_cooldown(2, 0.0)  # No third ability yet
